"""
Management command to set up default user roles for the flexible role system.
This creates the standard roles that replace the old boolean fields.
"""

from django.core.management.base import BaseCommand
from Ilearnova.models import UserRole


class Command(BaseCommand):
    help = "Set up default user roles for the flexible role system"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force recreation of roles even if they exist",
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Setting up default user roles..."))

        # Define default roles
        default_roles = [
            {
                "name": "School Owner",
                "code": "school_owner",
                "description": (
                    "Owner of the educational institution with full "
                    "administrative rights"
                ),
                "category": "admin",
                "requires_verification": True,
                "can_have_multiple": False,
            },
            {
                "name": "Administrator",
                "code": "administrator",
                "description": "Administrative staff with management privileges",
                "category": "admin",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "Educational Body",
                "code": "educational_body",
                "description": (
                    "Educational oversight and regulatory body representative"
                ),
                "category": "external",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "Teacher",
                "code": "teacher",
                "description": "Teaching staff member",
                "category": "academic",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "Student",
                "code": "student",
                "description": "Student enrolled in the institution",
                "category": "student",
                "requires_verification": False,
                "can_have_multiple": True,
            },
            {
                "name": "Parent",
                "code": "parent",
                "description": "Parent or guardian of a student",
                "category": "parent",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "Counselor",
                "code": "counselor",
                "description": "Student counselor or guidance counselor",
                "category": "support",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "Librarian",
                "code": "librarian",
                "description": "Library staff member",
                "category": "support",
                "requires_verification": True,
                "can_have_multiple": True,
            },
            {
                "name": "IT Administrator",
                "code": "it_admin",
                "description": "Information Technology administrator",
                "category": "support",
                "requires_verification": True,
                "can_have_multiple": True,
            },
        ]

        created_count = 0
        updated_count = 0

        for role_data in default_roles:
            role, created = UserRole.objects.get_or_create(
                code=role_data["code"], defaults=role_data
            )

            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f"Created role: {role.name}"))
            elif options["force"]:
                # Update existing role
                for key, value in role_data.items():
                    if key != "code":  # Don't update the code
                        setattr(role, key, value)
                role.save()
                updated_count += 1
                self.stdout.write(self.style.WARNING(f"Updated role: {role.name}"))
            else:
                self.stdout.write(
                    self.style.WARNING(f"Role already exists: {role.name}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"\nCompleted! Created {created_count} roles, "
                f"updated {updated_count} roles."
            )
        )

        # Display summary
        self.stdout.write("\nAvailable roles:")
        for role in UserRole.objects.all().order_by("category", "name"):
            status = "✓ Active" if role.is_active else "✗ Inactive"
            verification = (
                "Requires verification"
                if role.requires_verification
                else "No verification required"
            )
            self.stdout.write(
                f"  • {role.name} ({role.code}) - {role.category} - {status} - "
                f"{verification}"
            )
