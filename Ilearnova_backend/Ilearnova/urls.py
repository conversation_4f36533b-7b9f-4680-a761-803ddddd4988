from django.urls import path

from .views import (
    AnnouncementCreateView,
    AnnouncementListView,
    AssignmentView,
    AttendanceReportView,
    ClassesDetailView,
    CourseMaterialView,
    CreateClassView,
    EnrollStudent,
    ExamResultsView,
    ExamView,
    GetAllStudentsView,
    LoginView,
    ParentRegisterView,
    ProfileView,
    QuestionView,
    SignAttendanceView,
    StudentRegisterView,
    SubjectView,
    SubmitAssignmentView,
    TakeExamView,
    TeacherRegisterView,
    TopicView,
    VerifyUserView,
)

urlpatterns = [
    path("register/teacher/", TeacherRegisterView.as_view()),
    path("register/parent/", ParentRegisterView.as_view()),
    path("register/student/", StudentRegisterView.as_view()),
    path("login/", LoginView.as_view()),
    path(
        "verify/<str:role>/<str:username>/",
        VerifyUserView.as_view(),
        name="verify-user",
    ),
    path("profile/", ProfileView.as_view()),
    path("class/", CreateClassView.as_view()),
    path("class_details/<int:class_id>/", ClassesDetailView.as_view()),
    path("enroll/<int:class_id>/", EnrollStudent.as_view(), name="enroll-student"),
    path("subject/<int:class_id>/", SubjectView.as_view(), name="subject"),
    path("topic/<int:subject_id>/", TopicView.as_view(), name="topic"),
    path("get_your_students/", GetAllStudentsView.as_view(), name="get-your-students"),
    path("assignment/<int:topic_id>/", AssignmentView.as_view(), name="assignment"),
    path(
        "assignment/<int:assignment_id>/submit/",
        SubmitAssignmentView.as_view(),
        name="submit-assignment",
    ),
    path(
        "subjects/<int:subject_id>/sign-attendance/",
        SignAttendanceView.as_view(),
        name="sign-attendance",
    ),
    path(
        "attendance/<int:subject_id>/report/",
        AttendanceReportView.as_view(),
        name="attendance",
    ),
    path("api/subjects/<int:subject_id>/exams/", ExamView.as_view(), name="exams"),
    path("api/exams/<int:exam_id>/take/", TakeExamView.as_view(), name="take-exam"),
    path(
        "api/exams/<int:exam_id>/results/",
        ExamResultsView.as_view(),
        name="exam-results",
    ),
    path(
        "api/exams/<int:exam_id>/questions/",
        QuestionView.as_view(),
        name="exam-questions",
    ),
    path(
        "api/announcements/", AnnouncementListView.as_view(), name="announcement-list"
    ),
    path(
        "api/announcements/create/",
        AnnouncementCreateView.as_view(),
        name="announcement-create",
    ),
    path(
        "api/classes/<int:class_id>/materials/",
        CourseMaterialView.as_view(),
        name="class-materials",
    ),
]
