# Generated by Django 5.2.1 on 2025-05-27 16:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("Ilearnova", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Classes",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "students",
                    models.ManyToManyField(
                        blank=True, related_name="classes", to="Ilearnova.student"
                    ),
                ),
                (
                    "teacher",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="classes",
                        to="Ilearnova.teacher",
                    ),
                ),
            ],
        ),
    ]
