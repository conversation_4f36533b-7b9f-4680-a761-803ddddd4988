from django.contrib import admin

from .models import (
    Assignment,
    Classes,
    CustomUser,
    Exam,
    ExamResult,
    Parent,
    Question,
    Student,
    StudentResponse,
    StudentSubjectAttendance,
    Subject,
    Submission,
    Teacher,
    Topic,
)


@admin.register(CustomUser)
class CustomUserAdmin(admin.ModelAdmin):
    list_display = ("id", "username", "email", "is_teacher", "is_student", "is_parent")


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "user",
    )  # Removed 'bio' if it doesn't exist


@admin.register(Parent)
class ParentAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "user",
    )  # Removed 'phone_number' if it doesn't exist


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "user",
    )  # Removed 'parent' and 'class_assigned' if not fields


@admin.register(Classes)
class ClassesAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "name",
    )  # Removed 'description' if it doesn't exist


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "teacher")  # Removed 'class_assigned' if invalid


@admin.register(Topic)
class TopicAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "subject")


@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "topic")


@admin.register(StudentSubjectAttendance)
class StudentSubjectAttendanceAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "student",
        "subject",
    )


admin.site.register(Submission)
admin.site.register(Exam)
admin.site.register(Question)
admin.site.register(StudentResponse)


@admin.register(ExamResult)
class ExamResultsAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "student",
        "exam",
        "score",
        "total_marks",
        "percentage",
        "submitted_at",
    )
