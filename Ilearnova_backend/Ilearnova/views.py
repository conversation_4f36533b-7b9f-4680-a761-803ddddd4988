from django.conf import settings
from django.contrib.auth import authenticate, get_user_model
from django.core.mail import send_mail
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from core_app.notifications import notify_user

from .models import (
    Announcement,
    Assignment,
    Classes,
    Exam,
    ExamResult,
    Question,
    Student,
    StudentResponse,
    Subject,
    Submission,
    Topic,
)
from .serializers import (
    AnnouncementSerializer,
    AssignmentSerializer,
    AssignmentSubmissionSerializer,
    ClassesSerialzer,
    CourseMaterialSerializer,
    ExamResultSerializer,
    ExamSerializer,
    ParentRegisterSerializer,
    ProfileSerializer,
    QuestionSerializer,
    StudentRegisterSerializer,
    StudentSerializer,
    SubjectSerializer,
    TeacherRegisterSerializer,
    TopicSerializer,
)

User = get_user_model()


class TeacherRegisterView(generics.CreateAPIView):
    serializer_class = TeacherRegisterSerializer
    permission_classes = [AllowAny]


class ParentRegisterView(generics.CreateAPIView):
    serializer_class = ParentRegisterSerializer
    permission_classes = [AllowAny]


class StudentRegisterView(generics.CreateAPIView):
    serializer_class = StudentRegisterSerializer
    permission_classes = [AllowAny]


class LoginView(APIView):
    permission_classes = [AllowAny]
    serializer_class = TokenObtainPairSerializer

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")

        user = authenticate(username=username, password=password)

        if user:
            if user.is_teacher and not user.teacher.is_verified:
                return Response(
                    {"error": "Teacher not verified yet."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            if user.is_parent and not user.parent.is_verified:
                return Response(
                    {"error": "Parent not verified yet."},
                    status=status.HTTP_403_FORBIDDEN,
                )

            serializer = TokenObtainPairSerializer(
                data={"username": username, "password": password}
            )
            try:
                serializer.is_valid(raise_exception=True)
            except Exception:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            return Response(serializer.validated_data, status=status.HTTP_200_OK)

        return Response(
            {"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED
        )


class VerifyUserView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, role, username):
        user = get_object_or_404(User, username=username)
        from_email = settings.DEFAULT_FROM_EMAIL

        if role == "teacher" and hasattr(user, "teacher"):
            user.teacher.is_verified = True
            user.teacher.save()
            send_mail(
                "Verification Complete",
                "Your teacher account has been verified.",
                from_email,
                [user.email],
            )
            return HttpResponse(f"Teacher {user.username} verified successfully.")
        elif role == "parent" and hasattr(user, "parent"):
            user.parent.is_verified = True
            user.parent.save()
            send_mail(
                "Verification Complete",
                "Your parent account has been verified.",
                from_email,
                [user.email],
            )
            return HttpResponse(f"Parent {user.username} verified successfully.")
        return HttpResponse("Invalid verification link.", status=400)


class ProfileView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    serializer_class = ProfileSerializer

    def get(self, request):

        if not hasattr(request.user, "student"):
            return Response({"error": "User is not a student."}, status=403)

        try:
            student = Student.objects.get(user=request.user)
        except Student.DoesNotExist:
            return Response({"error": "Student profile not found."}, status=404)

        serializer = ProfileSerializer(student)
        return Response(serializer.data)

    def put(self, request):
        if not hasattr(request.user, "student"):
            return Response({"error": "User is not a student."}, status=403)
        try:
            student = Student.objects.get(user=request.user)
        except Student.DoesNotExist:
            return Response({"error": "Student profile not found."}, status=404)

        serializer = ProfileSerializer(student, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "Profile successfully updated", "data": serializer.data},
                status=200,
            )
        return Response(serializer.errors, status=400)


class CreateClassView(generics.CreateAPIView):
    serializer_class = ClassesSerialzer
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request, *args, **kwargs):
        classname = request.data.get("classname")

        if Classes.objects.filter(name=classname).exists():
            return Response(
                {"error": "Class name already exists."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not classname:
            return Response(
                {"error": "Class name is required."}, status=status.HTTP_400_BAD_REQUEST
            )
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can create classes."},
                status=status.HTTP_403_FORBIDDEN,
            )

        classes = Classes.objects.create(name=classname, teacher=request.user.teacher)
        serializer = ClassesSerialzer(classes)
        data = serializer.data
        data["student_count"] = classes.students.count()
        return Response(
            {
                "message": "Class sucessfully created",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    def get(self, request, *args, **kwargs):
        if not (request.user.is_teacher or request.user.is_student):
            return Response(
                {"error": "Only teachers can view classes."},
                status=status.HTTP_403_FORBIDDEN,
            )

        classes = Classes.objects.all()
        serializer = ClassesSerialzer(classes, many=True)
        data = serializer.data
        for idx, class_obj in enumerate(classes):
            data[idx]["student_count"] = class_obj.students.count()
        return Response(data, status=status.HTTP_200_OK)


class ClassesDetailView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request, class_id):
        classes = get_object_or_404(Classes, id=class_id)
        serializer = ClassesSerialzer(classes)
        return Response(serializer.data, status=status.HTTP_200_OK)


class EnrollStudent(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        user = request.user

        if not hasattr(user, "student"):
            return Response(
                {"message": "Only students can register for a class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        student = user.student
        if not class_id:
            return Response(
                {"error": "class_id is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            class_instance = Classes.objects.get(id=class_id)
        except Classes.DoesNotExist:
            return Response(
                {"error": "Class not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if class_instance.students.filter(id=student.id).exists():
            return Response(
                {
                    "message": f"You are already registered to "
                    f"the class '{class_instance.name}'."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add student to the class's students
        class_instance.students.add(student)
        class_instance.save()

        return Response(
            {
                "message": f"You have successfully registered to "
                f"the class '{class_instance.name}'."
            },
            status=status.HTTP_200_OK,
        )


class SubjectView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = SubjectSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can create subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        class_instance = get_object_or_404(Classes, id=class_id)
        if class_instance.teacher != request.user.teacher:
            return Response(
                {"error": "You can only create subjects for your own classes."},
                status=status.HTTP_403_FORBIDDEN,
            )
        subject_name = request.data.get("name")
        if class_instance.subjects.filter(name=subject_name).exists():
            return Response(
                {"error": "Subject with this name already exists in the class."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not subject_name:
            return Response(
                {"error": "Subject name is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = SubjectSerializer(data=request.data)
        if serializer.is_valid():
            subject = serializer.save(
                teacher=request.user.teacher,
                classname=class_instance,
            )
            return Response(
                {
                    "message": "Subject created successfully",
                    "data": SubjectSerializer(subject).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, class_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response(
                {"error": "Only teachers and students can view subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        class_instance = get_object_or_404(Classes, id=class_id)

        # If user is a student, check enrollment
        if request.user.is_student:
            student = request.user.student
            if not class_instance.students.filter(id=student.id).exists():
                return Response(
                    {"error": "You are not enrolled in this class."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        subjects = class_instance.subjects.all()
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TopicView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = TopicSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, subject_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can create topics."},
                status=status.HTTP_403_FORBIDDEN,
            )
        subject = get_object_or_404(Subject, id=subject_id)
        if subject.teacher != request.user.teacher:
            return Response(
                {"error": "You can only create topics for your own subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        topic_name = request.data.get("name")
        if not topic_name:
            return Response(
                {"error": "Topic name is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TopicSerializer(data=request.data)
        if serializer.is_valid():
            topic = serializer.save(subject=subject)
            return Response(
                {
                    "message": "Topic created successfully",
                    "data": TopicSerializer(topic).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, subject_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response(
                {"error": "Only teachers and students can view topics."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)

        # If user is a student, check enrollment in the class linked to the subject
        if request.user.is_student:
            student = request.user.student
            class_instance = subject.classname
            if not class_instance.students.filter(id=student.id).exists():
                return Response(
                    {"error": "You are not enrolled in the class for this subject."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        topics = subject.topics.all()
        serializer = TopicSerializer(topics, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GetAllStudentsView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = StudentSerializer

    authentication_classes = [JWTAuthentication]

    def get(self, request):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can view students."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Get students enrolled in any class taught by this teacher
        teacher = request.user.teacher
        classes = Classes.objects.filter(teacher=teacher)
        students = Student.objects.filter(classes__in=classes).distinct()
        serializer = StudentSerializer(students, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AssignmentView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AssignmentSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, topic_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can create assignments."},
                status=status.HTTP_403_FORBIDDEN,
            )

        topic = get_object_or_404(Topic, id=topic_id)
        if topic.subject.teacher != request.user.teacher:
            return Response(
                {"error": "You can only create assignments for your own subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = AssignmentSerializer(data=request.data)
        if serializer.is_valid():
            assignment = serializer.save(topic=topic, created_by=request.user)
            class_obj = topic.subject.classname
            students = class_obj.students.all()
            for student in students:
                notify_user(
                    student.user,
                    f"New assignment '{assignment.title}' has been posted "
                    f"for your class '{class_obj.name}'.",
                )
            return Response(
                {
                    "message": "Assignment created successfully",
                    "data": AssignmentSerializer(assignment).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, topic_id):
        if not request.user.is_teacher and not request.user.is_student:
            return Response(
                {"error": "Only teachers and students can view assignments."},
                status=status.HTTP_403_FORBIDDEN,
            )

        topic = get_object_or_404(Topic, id=topic_id)

        # If user is a student,
        # check enrollment in the class linked to the assignment's subject
        if request.user.is_student:
            student = request.user.student
            class_instance = topic.subject.classname
            if not class_instance.students.filter(id=student.id).exists():
                return Response(
                    {"error": "You are not enrolled in the class for this assignment."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        if request.user.is_teacher and topic.subject.teacher.user != request.user:
            return Response(
                {"error": "You can only view assignments for your own subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        assignments = topic.assignments.all()
        serializer = AssignmentSerializer(assignments, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class SubmitAssignmentView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]
    serializer_class = AssignmentSubmissionSerializer

    def post(self, request, assignment_id):
        if not request.user.is_student:
            return Response(
                {"error": "Only students can submit assignments."},
                status=status.HTTP_403_FORBIDDEN,
            )

        assignment = get_object_or_404(Assignment, id=assignment_id)
        student = request.user.student

        # Check if student is enrolled in the class linked to the assignment's subject
        class_instance = assignment.topic.subject.classname
        if not class_instance.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in the class for this assignment."},
                status=status.HTTP_403_FORBIDDEN,
            )

        if assignment.due_date and timezone.now() > assignment.due_date:
            return Response(
                {"error": "Assignment submission deadline has passed."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if Submission.objects.filter(assignment=assignment, student=student).exists():
            return Response(
                {"error": "You have already submitted this assignment."}, status=400
            )

        serializer = AssignmentSubmissionSerializer(data=request.data)
        if serializer.is_valid():
            submission = serializer.save(assignment=assignment, student=student)
            return Response(
                {
                    "message": "Submission created successfully",
                    "data": AssignmentSubmissionSerializer(submission).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, assignment_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can view submissions."},
                status=status.HTTP_403_FORBIDDEN,
            )

        assignment = get_object_or_404(Assignment, id=assignment_id)
        submissions = assignment.submissions.all()
        serializer = AssignmentSubmissionSerializer(submissions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ExamView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ExamSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, subject_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can create exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)
        if subject.teacher.user != request.user:
            return Response(
                {"error": "You can only create exams for your own subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = ExamSerializer(data=request.data)
        if serializer.is_valid():
            exam = serializer.save(subject=subject)
            return Response(
                {
                    "message": "Exam created successfully",
                    "data": ExamSerializer(exam).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, subject_id):
        subject = get_object_or_404(Subject, id=subject_id)

        # Teachers see all exams, students only see published ones
        if request.user.is_teacher and subject.teacher.user == request.user:
            exams = subject.exams.all()
        else:
            exams = subject.exams.filter(is_published=True)

        serializer = ExamSerializer(exams, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TakeExamView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request, exam_id):
        if not request.user.is_student:
            return Response(
                {"error": "Only students can take exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        exam = get_object_or_404(Exam, id=exam_id)
        student = request.user.student

        # Check if student is enrolled in the class
        if not exam.subject.classname.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Check if exam is active
        now = timezone.now()
        if not (exam.start_time <= now <= exam.end_time):
            return Response(
                {"error": "Exam is not currently active."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if student has already submitted
        if ExamResult.objects.filter(student=student, exam=exam).exists():
            return Response(
                {"error": "You have already submitted this exam."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        questions = exam.questions.all()
        serializer = QuestionSerializer(questions, many=True)
        return Response(
            {"exam": ExamSerializer(exam).data, "questions": serializer.data},
            status=status.HTTP_200_OK,
        )

    def post(self, request, exam_id):
        if not request.user.is_student:
            return Response(
                {"error": "Only students can submit exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        exam = get_object_or_404(Exam, id=exam_id)
        student = request.user.student

        # Check if student is enrolled
        if not exam.subject.classname.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Check if exam is still active
        now = timezone.now()
        if now > exam.end_time:
            return Response(
                {"error": "Exam submission time has ended."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not (exam.start_time <= now <= exam.end_time):
            return Response(
                {"error": "Exam is not currently active."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check for existing submission
        if ExamResult.objects.filter(student=student, exam=exam).exists():
            return Response(
                {"error": "You have already submitted this exam."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Process responses
        responses = request.data.get("responses", [])
        correct_count = 0
        total_marks = 0

        for response in responses:
            question_id = response.get("question")
            selected_option = response.get("selected_option")

            if not question_id or not selected_option:
                continue

            question = get_object_or_404(Question, id=question_id, exam=exam)
            StudentResponse.objects.create(
                student=student,
                exam=exam,
                question=question,
                selected_option=selected_option,
            )

            if selected_option == question.correct_option:
                correct_count += 1
                total_marks += question.mark

        # Calculate result
        percentage = (
            (total_marks / exam.num_of_questions) * 100
            if exam.num_of_questions > 0
            else 0
        )

        # Save final result
        ExamResult.objects.create(
            student=student,
            exam=exam,
            score=total_marks,
            total_marks=exam.total_marks,
            percentage=percentage,
        )

        return Response(
            {
                "message": "Exam submitted successfully",
                "score": total_marks,
                "total_marks": exam.total_marks,
                # "percentage": percentage,
                "total_questions": exam.num_of_questions,
            },
            status=status.HTTP_201_CREATED,
        )


class ExamResultsView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ExamResultSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request, exam_id):
        exam = get_object_or_404(Exam, id=exam_id)

        if request.user.is_teacher:
            # Teacher sees all results
            if exam.subject.teacher.user != request.user:
                return Response(
                    {"error": "You can only view results for your own exams."},
                    status=status.HTTP_403_FORBIDDEN,
                )
            results = exam.results.all()
            serializer = ExamResultSerializer(results, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        elif request.user.is_student:
            # Student sees only their result
            student = request.user.student
            result = get_object_or_404(ExamResult, student=student, exam=exam)
            serializer = ExamResultSerializer(result)
            return Response(serializer.data, status=status.HTTP_200_OK)

        elif request.user.is_parent:
            # Parent sees results of their children
            parent = request.user.parent
            # Assuming Parent model has a related_name 'children' for Student objects
            children = (
                parent.children.all()
            )  # or parent.students.all() depending on your model
            results = ExamResult.objects.filter(student__in=children, exam=exam)
            serializer = ExamResultSerializer(results, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

            # This return is unreachable and references undefined variables,
            # so remove it.


class QuestionView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = QuestionSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, exam_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can add questions."},
                status=status.HTTP_403_FORBIDDEN,
            )

        exam = get_object_or_404(Exam, id=exam_id)
        if exam.subject.teacher.user != request.user:
            return Response(
                {"error": "You can only add questions to your own exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        serializer = QuestionSerializer(data=request.data)
        if serializer.is_valid():
            question = serializer.save(exam=exam)
            return Response(
                {
                    "message": "Question added successfully",
                    "data": QuestionSerializer(question).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class IsTeacherOrSuperAdmin(IsAuthenticated):
    def has_permission(self, request, view):
        return super().has_permission(request, view) and (
            getattr(request.user, "is_teacher", False) or request.user.is_superuser
        )


class AnnouncementCreateView(APIView):
    permission_classes = [IsTeacherOrSuperAdmin]
    seraializer_class = AnnouncementSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        serializer = AnnouncementSerializer(data=request.data)
        if serializer.is_valid():
            announcement = serializer.save(created_by=request.user)
            # Notify all users
            for user in User.objects.all():
                notify_user(
                    user,
                    f"Announcement: {announcement.title} - {announcement.message}",
                )
            return Response(AnnouncementSerializer(announcement).data, status=201)
        return Response(serializer.errors, status=400)


class AnnouncementListView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = AnnouncementSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request):
        announcements = Announcement.objects.all().order_by("-created_at")
        serializer = self.serializer_class(announcements, many=True)
        return Response(serializer.data)


class CourseMaterialView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = CourseMaterialSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can upload materials."}, status=403
            )
        class_obj = get_object_or_404(Classes, id=class_id)
        serializer = CourseMaterialSerializer(data=request.data)
        if serializer.is_valid():
            material = serializer.save(class_obj=class_obj, uploaded_by=request.user)
            # Notify all students in the class
            for student in class_obj.students.all():
                notify_user(
                    student.user,
                    f"New course material '{material.title}' has been added "
                    f"to your class '{class_obj.name}'.",
                )
            return Response(CourseMaterialSerializer(material).data, status=201)
        return Response(serializer.errors, status=400)

    def get(self, request, class_id):
        class_obj = get_object_or_404(Classes, id=class_id)
        materials = class_obj.materials.all().order_by("-created_at")
        serializer = CourseMaterialSerializer(materials, many=True)
        return Response(serializer.data)
