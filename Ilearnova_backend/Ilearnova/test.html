<!DOCTYPE html>
<html>
<head>
    <title>Real-Time Notification Test</title>
</head>
<body>
    <h2>Real-Time Notifications</h2>
    <div>
        <strong>New messages this session: <span id="new-count">0</span></strong>
    </div>
    <ul id="notifications"></ul>

    <script>
        // Replace with your actual user token and WebSocket URL
        const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzQ5NDIwNjc0LCJpYXQiOjE3NDkzMzQyNzQsImp0aSI6IjhiN2EyYTA2YTllMzRmM2ViMzA4N2QxMjNlMjRjMjVjIiwidXNlcl9pZCI6M30.DEQSqouHOoW6x9eX5_B58KmFo2KlP_PjdmDIn4UIR9o"; // Set your JWT token here
        const ws_scheme = window.location.protocol === "https:" ? "wss" : "ws";
        const ws_url = ws_scheme + "://127.0.0.1:8000/ws/notifications/?token=" + token;

        const notificationsList = document.getElementById("notifications");
        const newCountSpan = document.getElementById("new-count");
        let newCount = 0;

        // Load notifications from localStorage
        let notifications = JSON.parse(localStorage.getItem("notifications") || "[]");
        notifications.forEach(msg => {
            const li = document.createElement("li");
            li.textContent = msg;
            notificationsList.appendChild(li);
        });

        // WebSocket for real-time notifications
        const socket = new WebSocket(ws_url);

        socket.onopen = function(e) {
            console.log("WebSocket connection established.");
        };

        socket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            console.log(data);
            const li = document.createElement("li");
            li.textContent = data.message;
            notificationsList.appendChild(li);

            // Save to localStorage
            notifications.push(data.message);
            localStorage.setItem("notifications", JSON.stringify(notifications));

            // Update new message count
            newCount += 1;
            newCountSpan.textContent = newCount;
        };

        socket.onclose = function(e) {
            console.log("WebSocket connection closed.");
        };

        socket.onerror = function(e) {
            console.error("WebSocket error:", e);
        };
    </script>
</body>
</html>