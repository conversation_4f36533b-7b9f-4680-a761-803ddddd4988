from rest_framework import serializers

from core_app.models import (
    Classes,
    CustomUser,
    Organization,
    Subject,
    UserOrganizationRole,
    UserRole,
)


class AdminUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "is_active",
            "date_joined",
        ]


class AdminOrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ["id", "name", "code", "type", "created_at", "is_active"]


class AdminClassesSerializer(serializers.ModelSerializer):
    teacher_role = serializers.PrimaryKeyRelatedField(
        queryset=UserOrganizationRole.objects.filter(
            role__code__in=["teacher", "admin"]
        ),
        required=True,
    )

    class Meta:
        model = Classes
        fields = ["id", "name", "organization", "teacher_role", "created_at"]


class AdminSubjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subject
        fields = ["id", "name", "classname", "organization", "created_at"]


class AdminUserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = ["id", "code", "name", "description"]


class AdminUserOrganizationRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserOrganizationRole
        fields = ["id", "user", "organization", "role", "created_at"]
