from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient

from core_app.models import (
    Classes,
    CustomUser,
    Organization,
    Subject,
    UserOrganizationRole,
    UserRole,
)


class AdminAppAPITestCase(TestCase):
    def setUp(self):
        # Create organization
        self.organization = Organization.objects.create(
            name="Test School",
            code="TESTSCH",
            is_active=True,
        )

        # Create roles
        self.admin_role_type = UserRole.objects.create(
            name="Admin", code="admin", category="admin"
        )
        self.teacher_role_type = UserRole.objects.create(
            name="Teacher", code="teacher", category="academic"
        )
        self.student_role_type = UserRole.objects.create(
            name="Student", code="student", category="student"
        )

        # Create users
        self.admin_user = CustomUser.objects.create_user(
            username="admin1",
            email="<EMAIL>",
            password="password123",
        )
        self.teacher_user = CustomUser.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="password123",
        )
        self.student_user = CustomUser.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="password123",
        )

        # Assign roles to users in organization
        self.admin_org_role = UserOrganizationRole.objects.create(
            user=self.admin_user,
            organization=self.organization,
            role=self.admin_role_type,
            is_active=True,
            is_verified=True,
        )
        self.teacher_org_role = UserOrganizationRole.objects.create(
            user=self.teacher_user,
            organization=self.organization,
            role=self.teacher_role_type,
            is_active=True,
            is_verified=True,
        )
        self.student_org_role = UserOrganizationRole.objects.create(
            user=self.student_user,
            organization=self.organization,
            role=self.student_role_type,
            is_active=True,
            is_verified=True,
        )

        # Create class and subject
        self.classroom = Classes.objects.create(
            name="JSS1",
            organization=self.organization,
            teacher_role=self.teacher_org_role,
        )
        self.subject = Subject.objects.create(
            name="Mathematics",
            organization=self.organization,
            teacher_role=self.teacher_org_role,
            classname=self.classroom,
        )

        self.client = APIClient()

    def test_admin_can_list_users(self):
        self.client.login(username="admin1", password="password123")
        url = reverse("admin-user-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        usernames = [u["username"] for u in response.data]
        self.assertIn("admin1", usernames)
        self.assertIn("teacher1", usernames)
        self.assertIn("student1", usernames)

    def test_admin_can_list_classes(self):
        self.client.login(username="admin1", password="password123")
        url = reverse("admin-classes-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        class_names = [c["name"] for c in response.data]
        self.assertIn("JSS1", class_names)

    def test_admin_can_create_class(self):
        self.client.login(username="admin1", password="password123")
        url = reverse("admin-classes-create")
        data = {
            "name": "JSS2",
            "teacher_role": self.admin_org_role.id,
            "organization": self.organization.id,
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)
        self.assertTrue(Classes.objects.filter(name="JSS2").exists())

    def test_admin_can_list_subjects(self):
        self.client.login(username="admin1", password="password123")
        url = reverse("admin-subjects-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        subject_names = [s["name"] for s in response.data]
        self.assertIn("Mathematics", subject_names)

    def test_non_admin_cannot_access_admin_endpoints(self):
        self.client.login(username="teacher1", password="password123")
        url = reverse("admin-user-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

        url = reverse("admin-classes-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

        url = reverse("admin-classes-create")
        data = {"name": "JSS3", "teacher_role": self.admin_org_role.id}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 403)

        url = reverse("admin-subjects-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 403)

    def test_data_is_filtered_by_admin_organization(self):
        # Create another org and class
        other_org = Organization.objects.create(
            name="Other School", code="OTHERSCH", is_active=True
        )
        other_admin = CustomUser.objects.create_user(
            username="admin2", email="<EMAIL>", password="password123"
        )
        other_admin_role = UserRole.objects.get_or_create(
            name="Admin", code="admin", category="admin"
        )[0]
        other_admin_org_role = UserOrganizationRole.objects.create(
            user=other_admin,
            organization=other_org,
            role=other_admin_role,
            is_active=True,
            is_verified=True,
        )
        Classes.objects.create(
            name="OtherClass",
            organization=other_org,
            teacher_role=other_admin_org_role,
        )
        self.client.login(username="admin2", password="password123")
        url = reverse("admin-classes-list")
        response = self.client.get(url)
        class_names = [c["name"] for c in response.data]
        self.assertIn("OtherClass", class_names)
        self.assertNotIn("JSS1", class_names)
