from rest_framework import generics, permissions

from core_app.models import (
    Classes,
    CustomUser,
    Organization,
    Subject,
    UserOrganizationRole,
)

from .serializers import (
    AdminClassesSerializer,
    AdminSubjectSerializer,
    AdminUserSerializer,
)


class IsOrganizationAdmin(permissions.BasePermission):
    """
    Allows access only to users who are admins of their organization.
    """

    def has_permission(self, request, view):
        # User must be authenticated and have an admin role in at least one organization
        return (
            request.user.is_authenticated
            and UserOrganizationRole.objects.filter(
                user=request.user,
                role__code="admin",  # Assumes 'admin' is the code for org/school admin
                organization__is_active=True,
            ).exists()
        )


class AdminUserListView(generics.ListAPIView):
    """
    List all users in the admin's organization.
    """

    serializer_class = AdminUserSerializer
    permission_classes = [permissions.IsAuthenticated, IsOrganizationAdmin]

    def get_queryset(self):
        # Get organizations where the user is an admin
        orgs = Organization.objects.filter(
            organization_user_roles__user=self.request.user,
            organization_user_roles__role__code="admin",
        )
        # Return all users in those organizations
        return CustomUser.objects.filter(
            user_organization_roles__organization__in=orgs
        ).distinct()


class AdminClassesListView(generics.ListAPIView):
    """
    List all classes in the admin's organization.
    """

    serializer_class = AdminClassesSerializer
    permission_classes = [permissions.IsAuthenticated, IsOrganizationAdmin]

    def get_queryset(self):
        orgs = Organization.objects.filter(
            organization_user_roles__user=self.request.user,
            organization_user_roles__role__code="admin",
        )
        return Classes.objects.filter(organization__in=orgs).distinct()


class AdminClassesCreateView(generics.CreateAPIView):
    """
    Create a class in the admin's organization.
    """

    serializer_class = AdminClassesSerializer
    permission_classes = [permissions.IsAuthenticated, IsOrganizationAdmin]

    def perform_create(self, serializer):
        # Assign the class to the first organization where the user is an admin
        org = Organization.objects.filter(
            organization_user_roles__user=self.request.user,
            organization_user_roles__role__code="admin",
        ).first()
        serializer.save(organization=org)


class AdminSubjectListView(generics.ListAPIView):
    """
    List all subjects in the admin's organization.
    """

    serializer_class = AdminSubjectSerializer
    permission_classes = [permissions.IsAuthenticated, IsOrganizationAdmin]

    def get_queryset(self):
        orgs = Organization.objects.filter(
            organization_user_roles__user=self.request.user,
            organization_user_roles__role__code="admin",
        )
        return Subject.objects.filter(organization__in=orgs).distinct()
