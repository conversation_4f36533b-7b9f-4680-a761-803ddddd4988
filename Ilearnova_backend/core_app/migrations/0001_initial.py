# Generated by Django 5.2.1 on 2025-06-27 10:21

import uuid

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Classes",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Class",
                "verbose_name_plural": "Classes",
                "ordering": ["organization", "name"],
            },
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "code",
                    models.CharField(
                        help_text="Unique organization code for registration",
                        max_length=20,
                        unique=True,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("address", models.TextField(blank=True, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                ("website", models.URLField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions "
                        "without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, "
                        "digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log"
                        " into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated "
                        "as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("bio", models.TextField(blank=True, null=True)),
                (
                    "profile_pic",
                    models.ImageField(blank=True, null=True, upload_to="profiles/"),
                ),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("date_of_birth", models.DateField(blank=True, null=True)),
                ("address", models.TextField(blank=True, null=True)),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="Global verification status"
                    ),
                ),
                (
                    "verification_token",
                    models.UUIDField(
                        blank=True, help_text="Token for email verification", null=True
                    ),
                ),
                (
                    "verification_sent_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the verification email was sent",
                        null=True,
                    ),
                ),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="User-specific additional data (e.g., individual)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get "
                        "all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="Exam",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True, null=True)),
                ("start_time", models.DateTimeField(default=django.utils.timezone.now)),
                ("end_time", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "duration_minutes",
                    models.PositiveIntegerField(
                        default=60, help_text="Duration in minutes"
                    ),
                ),
                (
                    "instructions",
                    models.TextField(
                        blank=True,
                        default="Make sure to read all instructions carefully "
                        "before starting the exam.",
                    ),
                ),
                ("total_marks", models.PositiveIntegerField(default=100)),
                ("is_published", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "num_of_questions",
                    models.PositiveIntegerField(
                        default=10, help_text="Number of questions in the exam"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_exams",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exams",
                        to="core_app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam",
                "verbose_name_plural": "Exams",
                "ordering": ["organization", "subject", "start_time"],
            },
        ),
        migrations.CreateModel(
            name="CourseMaterial",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "file",
                    models.FileField(
                        blank=True, null=True, upload_to="course_materials/"
                    ),
                ),
                ("link", models.URLField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "class_obj",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="materials",
                        to="core_app.classes",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="course_materials",
                        to="core_app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Course Material",
                "verbose_name_plural": "Course Materials",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddField(
            model_name="classes",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="classes",
                to="core_app.organization",
            ),
        ),
        migrations.CreateModel(
            name="BirthdayNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_read", models.BooleanField(default=False)),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="birthday_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="birthday_notifications",
                        to="core_app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Birthday Notification",
                "verbose_name_plural": "Birthday Notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Announcement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcements",
                        to="core_app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Announcement",
                "verbose_name_plural": "Announcements",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Question",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("text", models.TextField(default="Untitled question")),
                ("mark", models.PositiveIntegerField(default=1)),
                ("option_a", models.CharField(default="Option A", max_length=200)),
                ("option_b", models.CharField(default="Option B", max_length=200)),
                (
                    "option_c",
                    models.CharField(blank=True, default="Option C", max_length=200),
                ),
                (
                    "option_d",
                    models.CharField(blank=True, default="Option D", max_length=200),
                ),
                (
                    "correct_option",
                    models.CharField(
                        choices=[
                            ("A", "Option A"),
                            ("B", "Option B"),
                            ("C", "Option C"),
                            ("D", "Option D"),
                        ],
                        default="",
                        max_length=1,
                    ),
                ),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="questions",
                        to="core_app.exam",
                    ),
                ),
            ],
            options={
                "verbose_name": "Question",
                "verbose_name_plural": "Questions",
            },
        ),
        migrations.CreateModel(
            name="Subject",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "classname",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subjects",
                        to="core_app.classes",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subjects",
                        to="core_app.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Subject",
                "verbose_name_plural": "Subjects",
                "ordering": ["organization", "classname", "name"],
            },
        ),
        migrations.AddField(
            model_name="exam",
            name="subject",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="core_exams",
                to="core_app.subject",
            ),
        ),
        migrations.CreateModel(
            name="UserOrganizationRole",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="Role-specific verification status"
                    ),
                ),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                ("verified_at", models.DateTimeField(blank=True, null=True)),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Role-specific additional data "
                        "(e.g., employee ID, student ID, etc.)",
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_roles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organization_user_roles",
                        to="core_app.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_organization_roles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_roles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Organization Role",
                "verbose_name_plural": "User Organization Roles",
            },
        ),
        migrations.AddField(
            model_name="subject",
            name="teacher_role",
            field=models.ForeignKey(
                blank=True,
                help_text="Teacher assigned via role system",
                limit_choices_to={"role__code": "teacher"},
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="taught_subjects",
                to="core_app.userorganizationrole",
            ),
        ),
        migrations.AddField(
            model_name="classes",
            name="student_roles",
            field=models.ManyToManyField(
                blank=True,
                help_text="Students enrolled via role system",
                limit_choices_to={"role__code": "student"},
                related_name="enrolled_classes",
                to="core_app.userorganizationrole",
            ),
        ),
        migrations.AddField(
            model_name="classes",
            name="teacher_role",
            field=models.ForeignKey(
                blank=True,
                help_text="Teacher assigned via role system",
                limit_choices_to={"role__code": "teacher"},
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="taught_classes",
                to="core_app.userorganizationrole",
            ),
        ),
        migrations.CreateModel(
            name="UserRole",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                (
                    "code",
                    models.CharField(
                        help_text="Unique code for programmatic access",
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("admin", "Administrative"),
                            ("academic", "Academic"),
                            ("student", "Student"),
                            ("parent", "Parent/Guardian"),
                            ("support", "Support Staff"),
                            ("external", "External"),
                        ],
                        default="academic",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "requires_verification",
                    models.BooleanField(
                        default=False,
                        help_text="Whether users with this role need verification",
                    ),
                ),
                (
                    "can_have_multiple",
                    models.BooleanField(
                        default=True,
                        help_text="Whether a user can have multiple "
                        "instances of this role",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Default permissions for this role",
                        to="auth.permission",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Role",
                "verbose_name_plural": "User Roles",
                "ordering": ["category", "name"],
            },
        ),
        migrations.AddField(
            model_name="userorganizationrole",
            name="role",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="role_assignments",
                to="core_app.userrole",
            ),
        ),
        migrations.CreateModel(
            name="ExamResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("score", models.FloatField()),
                ("total_marks", models.PositiveIntegerField()),
                ("percentage", models.FloatField()),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="results",
                        to="core_app.exam",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_results",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Result",
                "verbose_name_plural": "Exam Results",
                "unique_together": {("student", "exam")},
            },
        ),
        migrations.CreateModel(
            name="StudentResponse",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("selected_option", models.CharField(max_length=1)),
                ("is_correct", models.BooleanField(default=False)),
                ("responded_at", models.DateTimeField(auto_now_add=True)),
                (
                    "exam",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_responses",
                        to="core_app.exam",
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core_app.question",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Response",
                "verbose_name_plural": "Student Responses",
                "unique_together": {("student", "question")},
            },
        ),
        migrations.CreateModel(
            name="Topic",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics",
                        to="core_app.organization",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics",
                        to="core_app.subject",
                    ),
                ),
            ],
            options={
                "verbose_name": "Topic",
                "verbose_name_plural": "Topics",
                "ordering": ["subject", "name"],
                "unique_together": {("name", "subject")},
            },
        ),
        migrations.AlterUniqueTogether(
            name="subject",
            unique_together={("name", "classname")},
        ),
        migrations.AlterUniqueTogether(
            name="classes",
            unique_together={("name", "organization")},
        ),
        migrations.AddIndex(
            model_name="userorganizationrole",
            index=models.Index(
                fields=["user", "organization"], name="core_app_us_user_id_2d97a7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userorganizationrole",
            index=models.Index(
                fields=["organization", "role"], name="core_app_us_organiz_0a8512_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="userorganizationrole",
            index=models.Index(
                fields=["user", "role"], name="core_app_us_user_id_e254ba_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="userorganizationrole",
            unique_together={("user", "organization", "role")},
        ),
    ]
