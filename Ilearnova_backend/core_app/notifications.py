from datetime import date

from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

from core_app.models import BirthdayNotification, CustomUser
from student_app.models import StudentProfile as Student

# TODO: Update import when Student is modularized


def notify_user(user, message):
    # print(f"Sending notification to user {user.id}: {message}")  # Debug print
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        f"user_{user.id}",
        {"type": "send_notification", "content": {"message": message}},
    )


def create_birthday_notifications():
    today = date.today()
    students = Student.objects.filter(
        birthday__month=today.month, birthday__day=today.day
    )
    teachers = CustomUser.objects.filter(
        user_organization_roles__role__code="teacher"
    ).distinct()
    for student in students:
        student_classes = student.classes.all()
        class_names = ", ".join([c.name for c in student_classes])
        # TODO: Determine organization from student or class context
        organization = None  # Placeholder, update when Student is modularized
        for teacher in teachers:
            msg = (
                f"Today is {student.user.username}'s birthday in class(es): "
                f"{class_names}."
            )
            BirthdayNotification.objects.create(
                recipient=teacher,
                organization=organization,
                message=msg,
            )
            notify_user(teacher, msg)
        msg = f"Happy Birthday! You are in class(es): {class_names}."
        BirthdayNotification.objects.create(
            recipient=student.user,
            organization=organization,
            message=msg,
        )
        notify_user(student.user, msg)
