import json

from channels.generic.websocket import AsyncWebsocketConsumer


class NotificationConsumer(AsyncWebsocketConsumer):
    group_name = None  # <-- Add this line

    async def connect(self):
        if self.scope["user"].is_anonymous:
            print("Anonymous user tried to connect.")
            await self.close()
        else:
            self.group_name = f"user_{self.scope['user'].id}"
            print(f"User {self.scope['user'].id} connected to group {self.group_name}")
            await self.channel_layer.group_add(self.group_name, self.channel_name)
            await self.accept()

    async def disconnect(self, close_code):
        if self.group_name:  # <-- Only try to discard if group_name is set
            await self.channel_layer.group_discard(self.group_name, self.channel_name)

    async def send_notification(self, event):
        print(f"WebSocket sending: {event['content']}")  # Debug print
        await self.send(text_data=json.dumps(event["content"]))
