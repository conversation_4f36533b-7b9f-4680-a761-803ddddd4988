import uuid

from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from core_app.notifications import notify_user

from .models import Announcement, BirthdayNotification, Organization, UserRole

User = get_user_model()


class CoreAppTests(APITestCase):
    def setUp(self):
        # Create roles
        self.admin_role = UserRole.objects.create(
            code="admin", name="Admin", is_active=True
        )
        self.teacher_role = UserRole.objects.create(
            code="teacher", name="Teacher", is_active=True
        )
        self.student_role = UserRole.objects.create(
            code="student", name="Student", is_active=True
        )
        self.parent_role = UserRole.objects.create(
            code="parent", name="Parent", is_active=True
        )
        self.individual_role = UserRole.objects.create(
            code="individual", name="Individual", is_active=True
        )
        self.school_admin_role = UserRole.objects.create(
            code="school_admin", name="School Admin", is_active=True
        )

        # Create organization
        self.organization = Organization.objects.create(
            name="Test School",
            address="123 Test St",
            email="<EMAIL>",
            phone_number="1234567890",
            code="TESTORG1",
            is_active=True,
        )

        # Create a verified user for login/profile tests
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            is_verified=True,
        )
        self.user.set_password("testpass123")
        self.user.save()

    def test_admin_registration(self):
        url = reverse("core_app:admin-register")
        data = {
            "username": "adminuser",
            "email": "<EMAIL>",
            "password": "adminpass123",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("user", response.data)
        self.assertIn("message", response.data)

    def test_teacher_registration(self):
        url = reverse("core_app:teacher-register")
        data = {
            "username": "teacheruser",
            "email": "<EMAIL>",
            "password": "teacherpass123",
            "organization_code": self.organization.code,
            "role_code": "teacher",
        }
        response = self.client.post(url, data, format="json")
        # print(response.json())
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("user", response.data)

    def test_student_registration(self):
        url = reverse("core_app:student-register")
        data = {
            "username": "studentuser",
            "email": "<EMAIL>",
            "password": "studentpass123",
            "organization_code": self.organization.code,
            "role_code": "student",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("user", response.data)

    def test_parent_registration(self):
        url = reverse("core_app:parent-register")
        data = {
            "username": "parentuser",
            "email": "<EMAIL>",
            "password": "parentpass123",
            "organization_code": self.organization.code,
            "role_code": "parent",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("user", response.data)

    def test_role_based_login(self):
        # Ensure user is in an organization and has a role
        self.user.is_verified = True
        self.user.save()
        if hasattr(self.user, "user_organization_roles"):
            self.user.user_organization_roles.create(
                organization=self.organization,
                role=self.admin_role,
                is_active=True,
                is_verified=True,
            )
        url = reverse("core_app:login")
        data = {
            "username": "testuser",
            "password": "testpass123",
            "organization_code": self.organization.code,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("access", response.data)
        self.assertIn("refresh", response.data)
        self.assertIn("user", response.data)

    def test_organization_list(self):
        url = reverse("core_app:organization-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(isinstance(response.data, list))

    def test_role_list(self):
        url = reverse("core_app:role-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(isinstance(response.data, list))

    def test_email_verification(self):
        # Simulate registration with a verification token (must be a valid UUID)
        token = uuid.uuid4()
        user = User.objects.create_user(
            username="verifyuser",
            email="<EMAIL>",
            password="verifypass123",
            is_verified=False,
            verification_token=token,
        )
        url = reverse("core_app:verify-email")
        response = self.client.get(url, {"token": str(token)})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        user.refresh_from_db()
        self.assertTrue(user.is_verified)
        self.assertIsNone(user.verification_token)

    def test_user_profile_get_and_update(self):
        self.client.force_authenticate(user=self.user)
        url = reverse("core_app:user-profile")
        # GET
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("username", response.data)
        # PUT
        update_data = {"first_name": "Updated"}
        response = self.client.put(url, update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("user", response.data)
        self.assertEqual(response.data["user"]["first_name"], "Updated")


class AnnouncementModelTest(APITestCase):
    def test_announcement_creation(self):
        org = Organization.objects.create(
            name="Announce Org",
            code="ANNORG",
            is_active=True,
        )
        user = User.objects.create_user(
            username="announceuser",
            email="<EMAIL>",
            password="announcepass",
            is_verified=True,
        )
        announcement = Announcement.objects.create(
            title="Test Announcement",
            message="This is a test announcement.",
            created_by=user,
            organization=org,
        )
        self.assertEqual(announcement.title, "Test Announcement")
        self.assertEqual(announcement.message, "This is a test announcement.")
        self.assertEqual(announcement.created_by, user)
        self.assertEqual(announcement.organization, org)


class BirthdayNotificationModelTest(APITestCase):
    def setUp(self):
        self.organization = Organization.objects.create(
            name="Birthday Org",
            code="BIRTHORG",
            is_active=True,
        )
        self.user = get_user_model().objects.create_user(
            username="birthdayuser",
            email="<EMAIL>",
            password="birthdaypass",
            is_verified=True,
        )

    def test_create_birthday_notification(self):
        notification = BirthdayNotification.objects.create(
            recipient=self.user,
            organization=self.organization,
            message="Happy Birthday!",
        )
        self.assertEqual(notification.recipient, self.user)
        self.assertEqual(notification.organization, self.organization)
        self.assertEqual(notification.message, "Happy Birthday!")
        self.assertFalse(notification.is_read)

    def test_notify_user_runs(self):
        # This test checks that notify_user runs without error.
        # If Channels is not configured, skip the test.
        try:
            notify_user(self.user, "Test notification")
        except AttributeError as e:
            if "'NoneType' object has no attribute 'group_send'" in str(e):
                # Channels not configured, expected in test environment
                pass
            else:
                self.fail(f"notify_user raised an unexpected exception: {e}")
        except Exception as e:
            self.fail(f"notify_user raised an unexpected exception: {e}")


class CourseMaterialModelTest(APITestCase):
    def setUp(self):
        from core_app.models import Classes, CustomUser, Organization

        self.organization = Organization.objects.create(
            name="Material Org",
            code="MATORG",
            is_active=True,
        )
        self.user = CustomUser.objects.create_user(
            username="materialuser",
            email="<EMAIL>",
            password="materialpass",
            is_verified=True,
        )
        self.class_obj = Classes.objects.create(
            name="Test Class",
            organization=self.organization,
        )

    def test_create_course_material(self):
        from core_app.models import CourseMaterial

        material = CourseMaterial.objects.create(
            title="Test Material",
            description="A test material.",
            class_obj=self.class_obj,
            uploaded_by=self.user,
            organization=self.organization,
        )
        self.assertEqual(material.title, "Test Material")
        self.assertEqual(material.class_obj, self.class_obj)
        self.assertEqual(material.uploaded_by, self.user)
        self.assertEqual(material.organization, self.organization)
