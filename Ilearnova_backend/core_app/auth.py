from rest_framework_simplejwt.authentication import JWTAuthentication


class BearerJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that automatically adds 'Bearer' prefix if missing
    """

    def get_header(self, request):
        header = super().get_header(request)
        if header and not header.decode("utf-8").startswith("Bearer "):
            # Add Bearer prefix if it's missing
            return f"Bearer {header.decode('utf-8')}".encode("utf-8")
        return header
