import datetime
import random
import string
import uuid

from django.conf import settings
from django.contrib.auth import authenticate, get_user_model, logout
from django.core.mail import send_mail
from django.shortcuts import get_object_or_404
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken

from .models import Organization, StudentSubjectAttendance, Subject, UserRole
from .serializers import (
    AdminRegistrationSerializer,
    LogoutSerializer,
    OrganizationSerializer,
    ParentRegistrationSerializer,
    RoleBasedLoginSerializer,
    StudentRegistrationSerializer,
    TeacherRegistrationSerializer,
    UserRoleSerializer,
    UserSelectsTypeSerializer,
    UserSerializer,
)


class RoleBasedPermission(permissions.BasePermission):
    """
    Custom permission class that checks user roles within organizations
    """

    required_roles = []

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        # Superusers have all permissions
        if request.user.is_superuser:
            return True

        # Check if user has any of the required roles
        if not self.required_roles:
            return True

        for role_code in self.required_roles:
            if request.user.has_role(role_code):
                return True
        return False


class TeacherPermission(RoleBasedPermission):
    required_roles = ["teacher", "admin"]


class StudentPermission(RoleBasedPermission):
    required_roles = ["student"]


class AdminPermission(RoleBasedPermission):
    required_roles = ["admin", "school_admin"]


# Authentication Views
class RoleBasedLoginView(APIView):
    """Enhanced login view with role-based authentication"""

    permission_classes = [AllowAny]

    @swagger_auto_schema(request_body=RoleBasedLoginSerializer)
    def post(self, request):
        serializer = RoleBasedLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        username = data["username"]
        password = data["password"]
        organization_code = data.get("organization_code")

        if not username or not password:
            return Response(
                {"error": "Username and password are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = authenticate(username=username, password=password)

        if not user:
            return Response(
                {"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED
            )

        # Check if user has roles in the specified organization (if provided)
        if organization_code:
            try:
                organization = Organization.objects.get(
                    code=organization_code, is_active=True
                )
                user_roles = user.get_roles_in_organization(organization)
                if not user_roles.filter(is_active=True).exists():
                    return Response(
                        {"error": "User has no active roles in this organization"},
                        status=status.HTTP_403_FORBIDDEN,
                    )
            except Organization.DoesNotExist:
                return Response(
                    {"error": "User has no active roles in this organization"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Generate JWT tokens
        serializer = TokenObtainPairSerializer(
            data={"username": username, "password": password}
        )
        try:
            serializer.is_valid(raise_exception=True)
            tokens = serializer.validated_data

            # Add user info and roles to response
            user_serializer = UserSerializer(user)
            response_data = {**tokens, "user": user_serializer.data}
            return Response(
                response_data,
                status=status.HTTP_200_OK,
            )

        except Exception:
            return Response(
                {"error": "Token generation failed"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class LogoutView(APIView):
    """
    Logout endpoint that invalidates JWT tokens and clears session data.
    Supports both token-based and session-based authentication.
    """

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=LogoutSerializer,
        operation_description="Logout user by invalidating tokens and clearing session",
        responses={
            200: "Successfully logged out",
            401: "Authentication required",
        },
    )
    def post(self, request):
        try:
            serializer = LogoutSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            refresh_token = serializer.validated_data.get("refresh_token")

            if refresh_token:
                try:
                    token = RefreshToken(refresh_token)
                    if hasattr(token, 'blacklist'):
                        token.blacklist()
                except Exception:
                    pass

            if hasattr(request, 'session'):
                logout(request)

            return Response(
                {"message": "Successfully logged out"},
                status=status.HTTP_205_RESET_CONTENT,
            )

        except Exception as e:
            return Response(
                {"error": "Logout failed", "detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


# Registration Views
class AdminRegistrationView(generics.CreateAPIView):
    """Admin registration with role-based system"""

    serializer_class = AdminRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            # Generate verification token and send email
            verification_token = uuid.uuid4()
            user.verification_token = verification_token
            user.verification_sent_at = timezone.now()
            user.is_verified = False
            user.save()

            verification_link = (
                f"{request.scheme}://{request.get_host()}"
                f"/api/verify-email/?token={verification_token}"
            )
            subject = "Activate your admin account"
            message = (
                f"Hello {user.username},\n\n"
                f"Please verify your email by clicking the link below:\n"
                f"{verification_link}\n\n"
                "If you did not register, please ignore this email."
            )
            send_mail(
                subject,
                message,
                None,  # Use default from email
                [user.email],
                fail_silently=True,
            )
            return Response(
                {
                    "message": (
                        "Admin registration successful."
                        "Please check your email to verify your account."
                    ),
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserSelectsType(APIView):
    """
    Endpoint for user to select account type after registration and email verification.
    Handles both 'individual' and 'organization' flows.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = UserSelectsTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        data = serializer.validated_data
        user = request.user

        if not user.first_time_user:
            return Response(
                {
                    "message": ("You have already selected your account type."),
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_200_OK,
            )

        if data["account_type"] == "individual":
            # Assign to default organization or set individual permissions
            default_org, created = Organization.objects.get_or_create(
                code="DEFAULT", is_active=True, name="ilearnova"
            )
            if default_org:
                # Assign user to default org as 'individual'
                role, created = UserRole.objects.get_or_create(
                    code="individual", is_active=True
                )
                if role or created:
                    user_org_role, created = user.user_organization_roles.get_or_create(
                        organization=default_org,
                        role=role,
                        defaults={"is_active": True},
                    )
            # Set individual permissions (customize as needed)
            user.is_individual = True
            user.first_time_user = False
            user.save()
            return Response(
                {
                    "message": (
                        "Account set as Individual Learner."
                        "You can now access public courses."
                    ),
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_200_OK,
            )

        elif data["account_type"] == "organization":
            # Generate unique 8-character organization code
            def generate_org_code():
                while True:
                    code = "".join(
                        random.choices(string.ascii_uppercase + string.digits, k=8)
                    )
                    if not Organization.objects.filter(code=code).exists():
                        return code

            org_code = generate_org_code()
            # Create new organization
            organization = Organization.objects.create(
                name=data["organization_name"],
                address=data["organization_address"],
                email=data["organization_email"],
                phone_number=data["organization_phone"],
                code=org_code,
                is_active=True,
            )
            # Set user as primary admin
            admin_role = UserRole.objects.filter(code="admin", is_active=True).first()
            if admin_role:
                user.user_organization_roles.create(
                    organization=organization,
                    role=admin_role,
                    is_active=True,
                    is_verified=True,
                )
            user.first_time_user = False
            user.save()
            # Send confirmation email
            try:
                subject = "Your School Organization Has Been Created"
                message = (
                    f"Dear {user.get_full_name() or user.username},\n\n"
                    f"Your organization '{organization.name}' has been created.\n"
                    f"Your unique organization code is: {org_code}\n\n"
                    "You have been set as the primary admin."
                    "Please use this code to invite other members."
                )
                send_mail(
                    subject,
                    message,
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    fail_silently=True,
                )
            except Exception:
                pass

            return Response(
                {
                    "message": (
                        "Educational Institution account created."
                        "Organization setup complete."
                    ),
                    "organization": OrganizationSerializer(organization).data,
                    "user": UserSerializer(user).data,
                    "organization_code": org_code,
                },
                status=status.HTTP_201_CREATED,
            )

        else:
            return Response(
                {"error": "Invalid account type."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TeacherRegistrationView(generics.CreateAPIView):
    """Teacher registration with role-based system"""

    serializer_class = TeacherRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                {
                    "message": (
                        "Teacher registration successful."
                        "Verification may be required."
                    ),
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class StudentRegistrationView(generics.CreateAPIView):
    """Student registration with role-based system"""

    serializer_class = StudentRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                {
                    "message": "Student registration successful.",
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ParentRegistrationView(generics.CreateAPIView):
    """Parent registration with role-based system"""

    serializer_class = ParentRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(
                {
                    "message": (
                        "Parent registration successful. Verification may be required."
                    ),
                    "user": UserSerializer(user).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Organization and Role Management Views
class OrganizationListView(generics.ListAPIView):
    """List all active organizations"""

    queryset = Organization.objects.filter(is_active=True)
    serializer_class = OrganizationSerializer
    permission_classes = [AllowAny]


class UserRoleListView(generics.ListAPIView):
    """List all active user roles"""

    queryset = UserRole.objects.filter(is_active=True)
    serializer_class = UserRoleSerializer
    permission_classes = [AllowAny]  # Public information


class VerifyEmailView(APIView):
    """Endpoint to verify user email via token"""

    permission_classes = [AllowAny]

    def get(self, request):

        token = request.query_params.get("token")
        if not token:
            return Response(
                {"error": "Token is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        User = get_user_model()
        try:
            user = User.objects.get(verification_token=token)
            user.is_verified = True
            user.verification_token = None
            user.save()
            return Response(
                {"message": "Email verified successfully."},
                status=status.HTTP_200_OK,
            )
        except User.DoesNotExist:
            return Response(
                {"error": "Invalid or expired token."},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UserProfileView(APIView):
    """Get and update user profile with role information"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

    def put(self, request):
        serializer = UserSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "Profile updated successfully", "user": serializer.data}
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# --- Attendance Views ---


class SignAttendanceView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def post(self, request, subject_id):
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can sign attendance."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)
        student = request.user.student

        # Check if student is enrolled in the class linked to the subject
        class_instance = subject.classname
        if not class_instance.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in the class for this subject."},
                status=status.HTTP_403_FORBIDDEN,
            )

        now = datetime.datetime.now()
        current_day = now.strftime("%A")

        if subject.day_of_week != current_day:
            return Response(
                {
                    "error": "You can only sign attendance"
                    " on the subject's scheduled day."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        now = timezone.localtime().time()
        if not (
            subject.start_time
            and subject.end_time
            and subject.start_time <= now <= subject.end_time
        ):
            return Response(
                {
                    "error": "You can only sign attendance"
                    "during the subject's scheduled time."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        attendance_obj, created = StudentSubjectAttendance.objects.get_or_create(
            student=student, subject=subject
        )
        attendance_obj.attendance_count += 1
        attendance_obj.save()

        return Response(
            {
                "message": f"Attendance signed for {subject.name}.",
                "attendance_count": attendance_obj.attendance_count,
            },
            status=status.HTTP_200_OK,
        )


class AttendanceReportView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request, subject_id):
        if not request.user.is_teacher:
            return Response(
                {"error": "Only teachers can view attendance reports."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)
        if subject.teacher.user != request.user:
            return Response(
                {
                    "error": "You can only view attendance "
                    "reports for your own subjects."
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        attendance_data = []
        for student in subject.classname.students.all():
            attendance_obj = StudentSubjectAttendance.objects.filter(
                student=student, subject=subject
            ).first()
            attendance_count = attendance_obj.attendance_count if attendance_obj else 0
            attendance_data.append(
                {"student": student.user.username, "attendance_count": attendance_count}
            )

        return Response(attendance_data, status=status.HTTP_200_OK)
