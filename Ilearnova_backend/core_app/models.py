import uuid

from django.contrib.auth.models import AbstractUser, Permission
from django.db import models
from django.utils import timezone


class Organization(models.Model):
    """
    Represents a school, educational institution, or organization.
    This enables multi-tenancy and data isolation.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique organization code for registration",
    )
    description = models.TextField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} ({self.code})"


class UserRole(models.Model):
    """
    Defines available user roles/types in the system.
    This allows dynamic addition of new roles without code changes.
    """

    ROLE_CATEGORIES = [
        ("admin", "Administrative"),
        ("academic", "Academic"),
        ("student", "Student"),
        ("parent", "Parent/Guardian"),
        ("support", "Support Staff"),
        ("external", "External"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(
        max_length=50, unique=True, help_text="Unique code for programmatic access"
    )
    description = models.TextField(blank=True, null=True)
    category = models.CharField(
        max_length=20, choices=ROLE_CATEGORIES, default="academic"
    )
    is_active = models.BooleanField(default=True)
    requires_verification = models.BooleanField(
        default=False, help_text="Whether users with this role need verification"
    )
    can_have_multiple = models.BooleanField(
        default=True,
        help_text="Whether a user can have multiple instances of this role",
    )
    permissions = models.ManyToManyField(
        Permission, blank=True, help_text="Default permissions for this role"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Role"
        verbose_name_plural = "User Roles"
        ordering = ["category", "name"]

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    """
    Extended user model with organization support and flexible role system.
    Removed static boolean fields in favor of dynamic role assignments.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    bio = models.TextField(blank=True, null=True)
    profile_pic = models.ImageField(upload_to="profiles/", blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    is_verified = models.BooleanField(
        default=False, help_text="Global verification status"
    )
    first_time_user = models.BooleanField(
        default=False, help_text="First time user status to determine account type"
    )
    is_individual = models.BooleanField(
        default=False, help_text="Individual learner status"
    )
    verification_token = models.UUIDField(
        blank=True, null=True, help_text="Token for email verification"
    )
    verification_sent_at = models.DateTimeField(
        blank=True, null=True, help_text="When the verification email was sent"
    )
    additional_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="User-specific additional data (e.g., individual)",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Role-based helper methods
    def has_role(self, role_code, organization=None):
        """Check if user has a specific role, optionally in a specific organization"""
        query = self.user_organization_roles.filter(
            role__code=role_code, is_active=True
        )
        if organization:
            query = query.filter(organization=organization)
        return query.exists()

    def get_role_organizations(self, role_code):
        """Get all organizations where user has a specific role"""
        return Organization.objects.filter(
            id__in=self.user_organization_roles.filter(
                role__code=role_code, is_active=True
            ).values_list("organization_id", flat=True)
        )

    def __str__(self):
        return self.username

    def get_roles_in_organization(self, organization):
        """Get all roles for this user in a specific organization"""
        return self.user_organization_roles.filter(organization=organization)

    def has_role_in_organization(self, role_code, organization):
        """Check if user has a specific role in an organization"""
        return self.user_organization_roles.filter(
            role__code=role_code, organization=organization, is_active=True
        ).exists()

    def get_organizations(self):
        """Get all organizations this user belongs to"""
        return Organization.objects.filter(
            id__in=self.user_organization_roles.values_list(
                "organization_id", flat=True
            )
        ).distinct()


class UserOrganizationRole(models.Model):
    """
    Links users to organizations with specific roles.
    Enables organization-scoped roles and multi-role assignments.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="user_organization_roles"
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="organization_user_roles"
    )
    role = models.ForeignKey(
        UserRole, on_delete=models.CASCADE, related_name="role_assignments"
    )
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(
        default=False, help_text="Role-specific verification status"
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_roles",
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="verified_roles",
    )
    additional_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Role-specific additional data (e.g., employee ID, student ID, etc.)",
    )

    class Meta:
        verbose_name = "User Organization Role"
        verbose_name_plural = "User Organization Roles"
        unique_together = ["user", "organization", "role"]
        indexes = [
            models.Index(fields=["user", "organization"]),
            models.Index(fields=["organization", "role"]),
            models.Index(fields=["user", "role"]),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.role.name} at {self.organization.name}"

    def save(self, *args, **kwargs):
        # Auto-verify if role doesn't require verification
        if not self.role.requires_verification and not self.is_verified:
            self.is_verified = True
            if not self.verified_at:
                self.verified_at = timezone.now()
        super().save(*args, **kwargs)


class Classes(models.Model):
    """
    Represents a class/course within an organization.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="classes"
    )
    # Role-based teacher assignment
    teacher_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name="taught_classes",
        null=True,
        blank=True,
        limit_choices_to={"role__code": "teacher"},
        help_text="Teacher assigned via role system",
    )
    # Role-based student enrollment
    student_roles = models.ManyToManyField(
        UserOrganizationRole,
        related_name="enrolled_classes",
        blank=True,
        limit_choices_to={"role__code": "student"},
        help_text="Students enrolled via role system",
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Class"
        verbose_name_plural = "Classes"
        unique_together = ["name", "organization"]
        ordering = ["organization", "name"]

    def __str__(self):
        return f"{self.name} ({self.organization.code})"


class Subject(models.Model):
    """
    Represents a subject within a class and organization.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="subjects"
    )
    # Role-based teacher assignment
    teacher_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name="taught_subjects",
        null=True,
        blank=True,
        limit_choices_to={"role__code": "teacher"},
        help_text="Teacher assigned via role system",
    )
    classname = models.ForeignKey(
        Classes, on_delete=models.CASCADE, related_name="subjects"
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Subject"
        verbose_name_plural = "Subjects"
        unique_together = ["name", "classname"]
        ordering = ["organization", "classname", "name"]

    def __str__(self):
        return f"{self.name} - {self.classname.name} ({self.organization.code})"

    def save(self, *args, **kwargs):
        # Auto-set organization from class
        if self.classname and not self.organization:
            self.organization = self.classname.organization
        super().save(*args, **kwargs)


class StudentSubjectAttendance(models.Model):
    """
    Tracks attendance of a student for a subject.
    """

    student = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="subject_attendance"
    )
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name="student_attendance"
    )
    attendance_count = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ("student", "subject")

    def __str__(self):
        return (
            f"{self.student.username} - {self.subject.name}: "
            f"{self.attendance_count}"
        )


class Topic(models.Model):
    """
    Represents a topic within a subject and organization.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name="topics"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="topics",
        null=True,
        blank=True,
    )
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Topic"
        verbose_name_plural = "Topics"
        unique_together = ["name", "subject"]
        ordering = ["subject", "name"]

    def __str__(self):
        return f"{self.name} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)


class Announcement(models.Model):
    """
    Represents an announcement within an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="announcements"
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="announcements"
    )

    class Meta:
        verbose_name = "Announcement"
        verbose_name_plural = "Announcements"
        ordering = ["-created_at"]

    def __str__(self):
        return self.title


class BirthdayNotification(models.Model):
    """
    Notification for user birthdays within an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="birthday_notifications"
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="birthday_notifications"
    )
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Birthday Notification"
        verbose_name_plural = "Birthday Notifications"
        ordering = ["-created_at"]

    def __str__(self):
        return f"To {self.recipient.username}: {self.message[:30]}"


class CourseMaterial(models.Model):
    """
    Represents course materials within an organization.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    file = models.FileField(upload_to="course_materials/", blank=True, null=True)
    link = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    class_obj = models.ForeignKey(
        Classes,
        on_delete=models.CASCADE,
        related_name="materials",
    )
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="course_materials"
    )

    class Meta:
        verbose_name = "Course Material"
        verbose_name_plural = "Course Materials"
        ordering = ["-created_at"]

    def __str__(self):
        return self.title


class Exam(models.Model):
    """
    Represents an exam within an organization.
    Merged from core_app and teacher_app.Examination.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    subject = models.ForeignKey(
        "Subject", on_delete=models.CASCADE, related_name="core_exams"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="exams",
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="created_exams",
        null=True,
        blank=True,
    )
    start_time = models.DateTimeField(default=timezone.now)
    end_time = models.DateTimeField(default=timezone.now)
    duration_minutes = models.PositiveIntegerField(
        help_text="Duration in minutes", default=60
    )
    instructions = models.TextField(
        blank=True,
        default="Make sure to read all instructions "
        "carefully before starting the exam.",
    )
    total_marks = models.PositiveIntegerField(default=100)
    is_published = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    num_of_questions = models.PositiveIntegerField(
        default=10, help_text="Number of questions in the exam"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Exam"
        verbose_name_plural = "Exams"
        ordering = ["organization", "subject", "start_time"]

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject if not provided
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)

    @property
    def is_currently_active(self):
        now = timezone.now()
        return self.start_time <= now <= self.end_time


class Question(models.Model):
    """
    Represents a question in an exam.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name="questions")
    text = models.TextField(default="Untitled question")
    mark = models.PositiveIntegerField(default=1)
    option_a = models.CharField(max_length=200, default="Option A")
    option_b = models.CharField(max_length=200, default="Option B")
    option_c = models.CharField(max_length=200, blank=True, default="Option C")
    option_d = models.CharField(max_length=200, blank=True, default="Option D")
    correct_option = models.CharField(
        max_length=1,
        choices=[
            ("A", "Option A"),
            ("B", "Option B"),
            ("C", "Option C"),
            ("D", "Option D"),
        ],
        default="",
    )

    class Meta:
        verbose_name = "Question"
        verbose_name_plural = "Questions"

    def __str__(self):
        return f"{self.exam.title} - Q{self.id}"


class StudentResponse(models.Model):
    """
    Represents a student's response to an exam question.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        "CustomUser", on_delete=models.CASCADE, related_name="exam_responses"
    )
    exam = models.ForeignKey(
        Exam, on_delete=models.CASCADE, related_name="student_responses"
    )
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    selected_option = models.CharField(max_length=1)
    is_correct = models.BooleanField(default=False)
    responded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Student Response"
        verbose_name_plural = "Student Responses"
        unique_together = ("student", "question")

    def save(self, *args, **kwargs):
        self.is_correct = self.selected_option == self.question.correct_option
        super().save(*args, **kwargs)


class ExamResult(models.Model):
    """
    Represents the result of a student for an exam.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        "CustomUser", on_delete=models.CASCADE, related_name="exam_results"
    )
    exam = models.ForeignKey(Exam, on_delete=models.CASCADE, related_name="results")
    score = models.FloatField()
    total_marks = models.PositiveIntegerField()
    percentage = models.FloatField()
    submitted_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Exam Result"
        verbose_name_plural = "Exam Results"
        unique_together = ("student", "exam")

    def __str__(self):
        return (
            f"{self.student.username} - {self.exam.title}: "
            f"{self.score}/{self.total_marks}"
        )
