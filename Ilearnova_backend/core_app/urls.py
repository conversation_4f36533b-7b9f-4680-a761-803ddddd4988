from django.urls import path

from .views import (
    AdminRegistrationView,
    OrganizationListView,
    ParentRegistrationView,
    RoleBasedLoginView,
    StudentRegistrationView,
    TeacherRegistrationView,
    UserProfileView,
    UserRoleListView,
    VerifyEmailView,
)

app_name = "core_app"

urlpatterns = [
    # Authentication
    path("auth/login/", RoleBasedLoginView.as_view(), name="login"),
    # Registration
    path(
        "auth/register/",
        AdminRegistrationView.as_view(),
        name="admin-register",
    ),
    path(
        "auth/register/teacher/",
        TeacherRegistrationView.as_view(),
        name="teacher-register",
    ),
    path(
        "auth/register/student/",
        StudentRegistrationView.as_view(),
        name="student-register",
    ),
    path(
        "auth/register/parent/",
        ParentRegistrationView.as_view(),
        name="parent-register",
    ),
    # User Profile
    path("auth/profile/", UserProfileView.as_view(), name="user-profile"),
    # Public Information
    path("organizations/", OrganizationListView.as_view(), name="organization-list"),
    path("roles/", UserRoleListView.as_view(), name="role-list"),
    # Email Verification
    path("verify-email/", VerifyEmailView.as_view(), name="verify-email"),
]
