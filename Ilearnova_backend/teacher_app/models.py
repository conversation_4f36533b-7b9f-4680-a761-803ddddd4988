import uuid

from django.db import models

from core_app.models import CustomUser, Organization, Subject, UserOrganizationRole


class TeacherProfile(models.Model):
    """
    Stores additional profile information for teachers.
    Linked to CustomUser and optionally UserOrganizationRole for org-scoped data.
    """

    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="teacher_profile"
    )
    user_org_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="teacher_profiles",
        limit_choices_to={"role__code": "teacher"},
        help_text="Teacher role assignment (optional, for org-scoped data)",
    )
    id_document = models.FileField(upload_to="ids/", blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Teacher Profile"
        verbose_name_plural = "Teacher Profiles"

    def __str__(self):
        return self.user.username


class Assignment(models.Model):
    """
    Represents an assignment within a subject and organization.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField()
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name="assignments"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="assignments",
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="created_assignments"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Assignment"
        verbose_name_plural = "Assignments"
        ordering = ["organization", "subject", "due_date"]

    def __str__(self):
        return f"{self.title} ({self.subject.name})"

    def save(self, *args, **kwargs):
        # Auto-set organization from subject
        if self.subject and not self.organization:
            self.organization = self.subject.organization
        super().save(*args, **kwargs)
