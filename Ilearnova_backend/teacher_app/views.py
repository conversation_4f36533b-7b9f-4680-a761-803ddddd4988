from django.shortcuts import get_object_or_404
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from core_app.models import Classes, Exam, Subject
from core_app.serializers import (
    AnnouncementSerializer,
    ClassesSerializer,
    CourseMaterialSerializer,
    ExamSerializer,
    SubjectSerializer,
    TopicSerializer,
)
from core_app.views import TeacherPermission
from student_app.models import ExamSubmission, Submission
from student_app.serializers import ExamSubmissionSerializer, SubmissionSerializer

from .models import Assignment
from .serializers import AssignmentSerializer


class CreateClassView(generics.CreateAPIView):
    serializer_class = ClassesSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]
    authentication_classes = [JWTAuthentication]

    def post(self, request, *args, **kwargs):
        classname = request.data.get("classname")

        if Classes.objects.filter(name=classname).exists():
            return Response(
                {"error": "Class name already exists."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        if not classname:
            return Response(
                {"error": "Class name is required."}, status=status.HTTP_400_BAD_REQUEST
            )
        if not hasattr(request.user, "teacher"):
            return Response(
                {"error": "Only teachers can create classes."},
                status=status.HTTP_403_FORBIDDEN,
            )

        classes = Classes.objects.create(name=classname, teacher=request.user.teacher)
        serializer = ClassesSerializer(classes)
        data = serializer.data
        data["student_count"] = classes.students.count()
        return Response(
            {
                "message": "Class successfully created",
                "data": data,
            },
            status=status.HTTP_201_CREATED,
        )

    def get(self, request, *args, **kwargs):
        if not (hasattr(request.user, "teacher") or hasattr(request.user, "student")):
            return Response(
                {"error": "Only teachers and students can view classes."},
                status=status.HTTP_403_FORBIDDEN,
            )

        classes = Classes.objects.all()
        serializer = ClassesSerializer(classes, many=True)
        data = serializer.data
        for idx, class_obj in enumerate(classes):
            data[idx]["student_count"] = class_obj.students.count()
        return Response(data, status=status.HTTP_200_OK)


class ClassesDetailView(APIView):
    permission_classes = [IsAuthenticated, TeacherPermission]
    authentication_classes = [JWTAuthentication]

    def get(self, request, class_id):
        classes = get_object_or_404(Classes, id=class_id)
        serializer = ClassesSerializer(classes)
        return Response(serializer.data, status=status.HTTP_200_OK)


class SubjectView(APIView):
    permission_classes = [IsAuthenticated, TeacherPermission]
    serializer_class = SubjectSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        if not hasattr(request.user, "teacher"):
            return Response(
                {"error": "Only teachers can create subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        class_instance = get_object_or_404(Classes, id=class_id)
        if class_instance.teacher != request.user.teacher:
            return Response(
                {"error": "You can only create subjects for your own classes."},
                status=status.HTTP_403_FORBIDDEN,
            )
        subject_name = request.data.get("name")
        if class_instance.subjects.filter(name=subject_name).exists():
            return Response(
                {"error": "Subject with this name already exists in the class."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not subject_name:
            return Response(
                {"error": "Subject name is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        serializer = SubjectSerializer(data=request.data)
        if serializer.is_valid():
            subject = serializer.save(
                teacher=request.user.teacher,
                classname=class_instance,
            )
            return Response(
                {
                    "message": "Subject created successfully",
                    "data": SubjectSerializer(subject).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, class_id):
        if not (hasattr(request.user, "teacher") or hasattr(request.user, "student")):
            return Response(
                {"error": "Only teachers and students can view subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        class_instance = get_object_or_404(Classes, id=class_id)

        # If user is a student, check enrollment
        if hasattr(request.user, "student"):
            student = request.user.student
            if not class_instance.students.filter(id=student.id).exists():
                return Response(
                    {"error": "You are not enrolled in this class."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        subjects = class_instance.subjects.all()
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TopicView(APIView):
    permission_classes = [IsAuthenticated, TeacherPermission]
    serializer_class = TopicSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, subject_id):
        if not hasattr(request.user, "teacher"):
            return Response(
                {"error": "Only teachers can create topics."},
                status=status.HTTP_403_FORBIDDEN,
            )
        subject = get_object_or_404(Subject, id=subject_id)
        if subject.teacher != request.user.teacher:
            return Response(
                {"error": "You can only create topics for your own subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        topic_name = request.data.get("name")
        if not topic_name:
            return Response(
                {"error": "Topic name is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        serializer = TopicSerializer(data=request.data)
        if serializer.is_valid():
            topic = serializer.save(subject=subject)
            return Response(
                {
                    "message": "Topic created successfully",
                    "data": TopicSerializer(topic).data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, subject_id):
        if not (hasattr(request.user, "teacher") or hasattr(request.user, "student")):
            return Response(
                {"error": "Only teachers and students can view topics."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)

        # If user is a student, check enrollment in the class linked to the subject
        if hasattr(request.user, "student"):
            student = request.user.student
            class_instance = subject.classname
            if not class_instance.students.filter(id=student.id).exists():
                return Response(
                    {"error": "You are not enrolled in the class for this subject."},
                    status=status.HTTP_403_FORBIDDEN,
                )

        topics = subject.topics.all()
        serializer = TopicSerializer(topics, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AnnouncementCreateView(APIView):
    permission_classes = [IsAuthenticated, TeacherPermission]
    serializer_class = AnnouncementSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request):
        serializer = AnnouncementSerializer(data=request.data)
        if serializer.is_valid():
            # Assume organization is determined from teacher's role
            user = request.user
            if not hasattr(user, "teacher"):
                return Response(
                    {"error": "Only teachers can create announcements."}, status=403
                )
            # Get teacher's organization (assume first one for now)
            orgs = user.get_organizations()
            if not orgs.exists():
                return Response({"error": "Teacher has no organization."}, status=400)
            announcement = serializer.save(created_by=user, organization=orgs.first())
            return Response(AnnouncementSerializer(announcement).data, status=201)
        return Response(serializer.errors, status=400)


class CourseMaterialView(APIView):
    permission_classes = [IsAuthenticated, TeacherPermission]
    serializer_class = CourseMaterialSerializer
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        user = request.user
        orgs = user.get_organizations()
        if not orgs.exists():
            return Response({"error": "Teacher has no organization."}, status=400)
        class_obj = get_object_or_404(Classes, id=class_id)
        serializer = CourseMaterialSerializer(data=request.data)
        if serializer.is_valid():
            material = serializer.save(
                class_obj=class_obj, uploaded_by=user, organization=orgs.first()
            )
            return Response(CourseMaterialSerializer(material).data, status=201)
        return Response(serializer.errors, status=400)

    def get(self, request, class_id):
        class_obj = get_object_or_404(Classes, id=class_id)
        materials = class_obj.materials.all().order_by("-created_at")
        serializer = CourseMaterialSerializer(materials, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TeacherAssignmentListCreateView(generics.ListCreateAPIView):
    """List and create assignments for teachers"""

    serializer_class = AssignmentSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter assignments by teacher's organizations"""
        user = self.request.user
        # Get all organizations where user has teacher role
        teacher_orgs = user.get_role_organizations("teacher")
        return Assignment.objects.filter(
            organization__in=teacher_orgs, created_by=user
        ).order_by("-created_at")

    def perform_create(self, serializer):
        """Set the created_by field to current user"""
        serializer.save(created_by=self.request.user)


class TeacherAssignmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a specific assignment"""

    serializer_class = AssignmentSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter assignments by teacher's organizations"""
        user = self.request.user
        if not user.is_authenticated:
            return Assignment.objects.none()
        teacher_orgs = user.get_role_organizations("teacher")
        return Assignment.objects.filter(organization__in=teacher_orgs, created_by=user)


class TeacherExaminationListCreateView(generics.ListCreateAPIView):
    """List and create exams for teachers"""

    serializer_class = ExamSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter exams by teacher's organizations"""
        user = self.request.user
        if not user.is_authenticated:
            return Exam.objects.none()
        teacher_orgs = user.get_role_organizations("teacher")
        return Exam.objects.filter(
            organization__in=teacher_orgs, created_by=user
        ).order_by("-created_at")

    def perform_create(self, serializer):
        """Set the created_by field to current user"""
        serializer.save(created_by=self.request.user)


class TeacherExaminationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a specific exam"""

    serializer_class = ExamSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        """Filter exams by teacher's organizations"""
        user = self.request.user
        if not user.is_authenticated:
            return Exam.objects.none()
        teacher_orgs = user.get_role_organizations("teacher")
        return Exam.objects.filter(organization__in=teacher_orgs, created_by=user)


class AssignmentSubmissionsView(generics.ListAPIView):
    """View all submissions for a specific assignment"""

    serializer_class = SubmissionSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        assignment_id = self.kwargs["assignment_id"]
        assignment = get_object_or_404(Assignment, id=assignment_id)

        # Check if teacher has permission to view this assignment
        user = self.request.user
        if assignment.created_by != user:
            return Submission.objects.none()

        return Submission.objects.filter(assignment=assignment).order_by(
            "-submitted_at"
        )


class ExaminationSubmissionsView(generics.ListAPIView):
    """View all submissions for a specific exam"""

    serializer_class = ExamSubmissionSerializer
    permission_classes = [IsAuthenticated, TeacherPermission]

    def get_queryset(self):
        examination_id = self.kwargs["examination_id"]
        examination = get_object_or_404(Exam, id=examination_id)

        # Check if teacher has permission to view this exam
        user = self.request.user
        if examination.created_by != user:
            return ExamSubmission.objects.none()

        return ExamSubmission.objects.filter(examination=examination).order_by(
            "-submitted_at"
        )
