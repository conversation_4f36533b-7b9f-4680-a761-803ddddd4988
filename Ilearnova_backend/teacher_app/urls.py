from django.urls import path

from .views import (
    AnnouncementCreateView,
    AssignmentSubmissionsView,
    ClassesDetailView,
    CourseMaterialView,
    CreateClassView,
    ExaminationSubmissionsView,
    SubjectView,
    TeacherAssignmentDetailView,
    TeacherAssignmentListCreateView,
    TeacherExaminationDetailView,
    TeacherExaminationListCreateView,
    TopicView,
)

app_name = "teacher_app"

urlpatterns = [
    # Class management
    path(
        "classes/",
        CreateClassView.as_view(),
        name="class-list-create",
    ),
    path(
        "classes/<uuid:class_id>/",
        ClassesDetailView.as_view(),
        name="class-detail",
    ),
    # Subject and topic management
    path(
        "classes/<uuid:class_id>/subjects/",
        SubjectView.as_view(),
        name="class-subjects",
    ),
    path(
        "subjects/<uuid:subject_id>/topics/",
        TopicView.as_view(),
        name="subject-topics",
    ),
    # Course materials
    path(
        "classes/<uuid:class_id>/materials/",
        CourseMaterialView.as_view(),
        name="class-materials",
    ),
    # Announcements
    path(
        "announcements/create/",
        AnnouncementCreateView.as_view(),
        name="announcement-create",
    ),
    # Assignment management
    path(
        "assignments/",
        TeacherAssignmentListCreateView.as_view(),
        name="teacher-assignment-list-create",
    ),
    path(
        "assignments/<uuid:pk>/",
        TeacherAssignmentDetailView.as_view(),
        name="teacher-assignment-detail",
    ),
    path(
        "assignments/<int:assignment_id>/submissions/",
        AssignmentSubmissionsView.as_view(),
        name="assignment-submissions",
    ),
    # Examination management
    path(
        "examinations/",
        TeacherExaminationListCreateView.as_view(),
        name="examination-list-create",
    ),
    path(
        "examinations/<uuid:pk>/",
        TeacherExaminationDetailView.as_view(),
        name="examination-detail",
    ),
    path(
        "examinations/<uuid:examination_id>/submissions/",
        ExaminationSubmissionsView.as_view(),
        name="examination-submissions",
    ),
]
