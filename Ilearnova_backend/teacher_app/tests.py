from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient, APITestCase

from core_app.models import (
    Classes,
    Exam,
    Organization,
    Subject,
    UserOrganizationRole,
    UserRole,
)
from core_app.serializers import ExamSerializer

from .models import Assignment, TeacherProfile
from .serializers import AssignmentSerializer

User = get_user_model()


class AssignmentModelTest(TestCase):
    def test_assignment_creation(self):
        organization = Organization.objects.create(name="Test Org", code="ORG1")
        classname = Classes.objects.create(name="Class 1", organization=organization)
        subject = Subject.objects.create(
            name="Math", classname=classname, organization=organization
        )
        user = User.objects.create_user(username="assigner", password="pass")
        assignment = Assignment.objects.create(
            title="Test Assignment",
            description="Test description",
            subject=subject,
            due_date=timezone.now() + timezone.timedelta(days=7),
            created_by=user,
        )
        self.assertEqual(str(assignment), "Test Assignment (Math)")


class ExamModelTest(TestCase):
    def test_exam_creation(self):
        organization = Organization.objects.create(name="Test Org", code="ORG1B")
        classname = Classes.objects.create(name="Class 2", organization=organization)
        subject = Subject.objects.create(
            name="Science", classname=classname, organization=organization
        )
        user = User.objects.create_user(username="examiner", password="pass")
        exam = Exam.objects.create(
            title="Test Exam",
            description="Test exam description",
            subject=subject,
            start_time=timezone.now() + timezone.timedelta(days=1),
            end_time=timezone.now() + timezone.timedelta(days=1, hours=2),
            created_by=user,
        )
        self.assertEqual(str(exam), "Test Exam (Science)")


class AssignmentSerializerTest(TestCase):
    def test_assignment_serializer_valid(self):
        organization = Organization.objects.create(name="Test Org", code="ORG2")
        classname = Classes.objects.create(name="Class 3", organization=organization)
        subject = Subject.objects.create(
            name="Math", classname=classname, organization=organization
        )
        data = {
            "title": "Assignment 1",
            "description": "Desc",
            "subject": subject.id,
            "due_date": (timezone.now() + timezone.timedelta(days=7)).isoformat(),
        }
        serializer = AssignmentSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_assignment_serializer_invalid(self):
        data = {"title": "", "description": "Desc", "subject": ""}
        serializer = AssignmentSerializer(data=data)
        self.assertFalse(serializer.is_valid())


class ExamSerializerTest(TestCase):
    def test_exam_serializer_valid(self):
        organization = Organization.objects.create(name="Test Org", code="ORG2B")
        classname = Classes.objects.create(name="Class 4", organization=organization)
        subject = Subject.objects.create(
            name="Science", classname=classname, organization=organization
        )
        data = {
            "title": "Exam 1",
            "description": "Desc",
            "subject": subject.id,
            "start_time": (timezone.now() + timezone.timedelta(days=1)).isoformat(),
            "end_time": (
                timezone.now() + timezone.timedelta(days=1, hours=2)
            ).isoformat(),
        }
        serializer = ExamSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_exam_serializer_invalid(self):
        data = {"title": "", "description": "Desc", "subject": ""}
        serializer = ExamSerializer(data=data)
        self.assertFalse(serializer.is_valid())


class AssignmentAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(username="teacher", password="pass")
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.organization = Organization.objects.create(name="Test Org", code="ORG3")
        # Assign teacher role to user for this organization
        from core_app.models import UserOrganizationRole, UserRole

        teacher_role, _ = UserRole.objects.get_or_create(
            code="teacher", defaults={"name": "Teacher"}
        )
        UserOrganizationRole.objects.create(
            user=self.user,
            organization=self.organization,
            role=teacher_role,
            is_active=True,
            is_verified=True,
        )
        self.classname = Classes.objects.create(
            name="Class 5", organization=self.organization
        )
        self.subject = Subject.objects.create(
            name="Math", classname=self.classname, organization=self.organization
        )
        self.assignment = Assignment.objects.create(
            title="API Assignment",
            description="API Desc",
            subject=self.subject,
            due_date=timezone.now() + timezone.timedelta(days=7),
            created_by=self.user,
        )

    def test_list_assignments(self):
        url = reverse("teacher_app:teacher-assignment-list-create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_create_assignment(self):
        url = reverse("teacher_app:teacher-assignment-list-create")
        subject = Subject.objects.create(
            name="English", classname=self.classname, organization=self.organization
        )
        data = {
            "title": "New Assignment",
            "description": "New Desc",
            "subject": subject.id,
            "due_date": (timezone.now() + timezone.timedelta(days=7)).isoformat(),
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, 201)

    def test_retrieve_assignment(self):
        url = reverse(
            "teacher_app:teacher-assignment-detail", args=[self.assignment.id]
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)

    def test_update_assignment(self):
        url = reverse(
            "teacher_app:teacher-assignment-detail", args=[self.assignment.id]
        )
        data = {
            "title": "Updated Assignment",
            "description": "Updated Desc",
            "subject": self.subject.id,
            "due_date": (timezone.now() + timezone.timedelta(days=7)).isoformat(),
        }
        response = self.client.put(url, data)

        self.assertEqual(response.status_code, 200)

    def test_delete_assignment(self):
        url = reverse(
            "teacher_app:teacher-assignment-detail", args=[self.assignment.id]
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 204)


class TeacherProfileModelTest(TestCase):
    def test_teacher_profile_creation(self):

        # Create user, org, and teacher role
        user = User.objects.create_user(
            username="teacherprofile", password="pass", email="<EMAIL>"
        )
        org = Organization.objects.create(name="Test Org", code="TPORG")
        teacher_role, _ = UserRole.objects.get_or_create(
            code="teacher", defaults={"name": "Teacher"}
        )
        user_org_role = UserOrganizationRole.objects.create(
            user=user,
            organization=org,
            role=teacher_role,
            is_active=True,
            is_verified=True,
        )
        # Create TeacherProfile
        profile = TeacherProfile.objects.create(
            user=user,
            user_org_role=user_org_role,
            id_document=None,
            phone_number="1234567890",
            address="123 Main St",
            is_verified=True,
        )
        self.assertEqual(profile.user, user)
        self.assertEqual(profile.user_org_role, user_org_role)
        self.assertEqual(profile.phone_number, "1234567890")
        self.assertEqual(profile.address, "123 Main St")
        self.assertTrue(profile.is_verified)


class ExamAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(username="teacher2", password="pass")
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.organization = Organization.objects.create(name="Test Org", code="ORG3B")
        # Assign teacher role to user for this organization
        from core_app.models import UserOrganizationRole, UserRole

        teacher_role, _ = UserRole.objects.get_or_create(
            code="teacher", defaults={"name": "Teacher"}
        )
        UserOrganizationRole.objects.create(
            user=self.user,
            organization=self.organization,
            role=teacher_role,
            is_active=True,
            is_verified=True,
        )
        self.classname = Classes.objects.create(
            name="Class 6", organization=self.organization
        )
        self.subject = Subject.objects.create(
            name="Science", classname=self.classname, organization=self.organization
        )
        self.exam = Exam.objects.create(
            title="API Exam",
            description="API Exam Desc",
            subject=self.subject,
            start_time=timezone.now() + timezone.timedelta(days=1),
            end_time=timezone.now() + timezone.timedelta(days=1, hours=2),
            created_by=self.user,
        )

    def test_list_exams(self):
        url = reverse("teacher_app:examination-list-create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_create_exam(self):
        url = reverse("teacher_app:examination-list-create")
        subject = Subject.objects.create(
            name="English", classname=self.classname, organization=self.organization
        )
        data = {
            "title": "New Exam",
            "description": "Exam Desc",
            "subject": subject.id,
            "start_time": (timezone.now() + timezone.timedelta(days=1)).isoformat(),
            "end_time": (
                timezone.now() + timezone.timedelta(days=1, hours=2)
            ).isoformat(),
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_retrieve_exam(self):
        url = reverse("teacher_app:examination-detail", args=[self.exam.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_update_exam(self):
        url = reverse("teacher_app:examination-detail", args=[self.exam.id])
        data = {
            "title": "Updated Exam",
            "description": "Updated Desc",
            "subject": self.subject.id,
            "start_time": (timezone.now() + timezone.timedelta(days=1)).isoformat(),
            "end_time": (
                timezone.now() + timezone.timedelta(days=1, hours=2)
            ).isoformat(),
        }
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, 200)

    def test_delete_exam(self):
        url = reverse("teacher_app:examination-detail", args=[self.exam.id])
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 204)
