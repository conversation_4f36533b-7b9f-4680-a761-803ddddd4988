from rest_framework import serializers

from .models import Assignment


class AssignmentSerializer(serializers.ModelSerializer):
    """Serializer for Assignment model"""

    subject_name = serializers.Char<PERSON>ield(source="subject.name", read_only=True)
    class_name = serializers.Char<PERSON>ield(source="subject.classname.name", read_only=True)
    organization_name = serializers.CharField(
        source="organization.name", read_only=True
    )
    created_by_name = serializers.CharField(
        source="created_by.get_full_name", read_only=True
    )
    submission_count = serializers.SerializerMethodField()

    class Meta:
        model = Assignment
        fields = [
            "id",
            "title",
            "description",
            "due_date",
            "subject",
            "subject_name",
            "class_name",
            "organization",
            "organization_name",
            "created_by",
            "created_by_name",
            "is_active",
            "created_at",
            "submission_count",
        ]
        read_only_fields = [
            "id",
            "organization",
            "created_by",
            "created_at",
            "subject_name",
            "class_name",
            "organization_name",
            "created_by_name",
            "submission_count",
        ]

    def get_submission_count(self, obj):
        return obj.submissions.count()

    def validate_subject(self, value):
        """
        Validate that the user has permission to create assignments for this subject"""
        request = self.context.get("request")
        if request and request.user:
            # Check if user has teacher role in the subject's organization
            if not request.user.has_role_in_organization("teacher", value.organization):
                raise serializers.ValidationError(
                    "You don't have permission to create assignments for this subject"
                )
        return value

    def create(self, validated_data):
        """Create assignment with proper organization and user assignment"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user
            # Organization will be auto-set by the model's save method
        return super().create(validated_data)


class AssignmentCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating assignments"""

    class Meta:
        model = Assignment
        fields = ["title", "description", "due_date", "subject"]

    def create(self, validated_data):
        request = self.context.get("request")
        if request and request.user:
            validated_data["created_by"] = request.user
        return super().create(validated_data)

    def validate_subject(self, value):
        """
        Validate that the user has permission to create assignments for this subject"""
        request = self.context.get("request")
        if request and request.user:
            # Check if user has teacher role in the subject's organization
            if not request.user.has_role_in_organization("teacher", value.organization):
                raise serializers.ValidationError(
                    "You don't have permission to create assignments for this subject"
                )
        return value
