# Generated by Django 5.2.1 on 2025-06-27 10:21

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ParentProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "id_document",
                    models.FileField(blank=True, null=True, upload_to="ids/"),
                ),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parent_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_org_role",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent role assignment "
                        "(optional, for org-scoped data)",
                        limit_choices_to={"role__code": "parent"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="parent_profiles",
                        to="core_app.userorganizationrole",
                    ),
                ),
            ],
            options={
                "verbose_name": "Parent Profile",
                "verbose_name_plural": "Parent Profiles",
            },
        ),
        migrations.CreateModel(
            name="ParentStudentRelationship",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "relationship",
                    models.CharField(
                        choices=[
                            ("mother", "Mother"),
                            ("father", "Father"),
                            ("guardian", "Guardian"),
                            ("other", "Other"),
                        ],
                        default="parent",
                        max_length=32,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "parent",
                    models.ForeignKey(
                        help_text="Parent user (must have parent role)",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children_relationships",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        help_text="Student user (must have student role)",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parent_relationships",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Parent-Student Relationship",
                "verbose_name_plural": "Parent-Student Relationships",
                "unique_together": {("parent", "student")},
            },
        ),
    ]
