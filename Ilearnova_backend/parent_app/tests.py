from django.core.exceptions import ValidationError
from django.test import TestCase

from core_app.models import CustomUser, Organization, UserOrganizationRole, UserRole
from parent_app.models import ParentStudentRelationship


class ParentStudentRelationshipModelTest(TestCase):
    def setUp(self):
        # Create organization
        self.organization = Organization.objects.create(
            name="Test School",
            code="TESTSCH",
            is_active=True,
        )

        # Create roles
        self.parent_role_type = UserRole.objects.create(
            name="Parent", code="parent", category="parent"
        )
        self.student_role_type = UserRole.objects.create(
            name="Student", code="student", category="student"
        )

        # Create users
        self.parent_user = CustomUser.objects.create_user(
            username="parent1",
            email="<EMAIL>",
            password="password123",
        )
        self.student_user = CustomUser.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="password123",
        )

        # Assign roles to users in organization
        self.parent_org_role = UserOrganizationRole.objects.create(
            user=self.parent_user,
            organization=self.organization,
            role=self.parent_role_type,
            is_active=True,
            is_verified=True,
        )
        self.student_org_role = UserOrganizationRole.objects.create(
            user=self.student_user,
            organization=self.organization,
            role=self.student_role_type,
            is_active=True,
            is_verified=True,
        )

    def test_create_parent_student_relationship(self):
        rel = ParentStudentRelationship.objects.create(
            parent=self.parent_user,
            student=self.student_user,
            relationship="father",
        )
        self.assertEqual(rel.parent, self.parent_user)
        self.assertEqual(rel.student, self.student_user)
        self.assertEqual(rel.relationship, "father")
        self.assertIn("father", str(rel))

    def test_unique_constraint(self):
        ParentStudentRelationship.objects.create(
            parent=self.parent_user,
            student=self.student_user,
            relationship="mother",
        )
        with self.assertRaises(ValidationError) as cm:
            ParentStudentRelationship.objects.create(
                parent=self.parent_user,
                student=self.student_user,
                relationship="mother",
            )
        self.assertIn(
            "Parent-Student Relationship with this Parent and Student already exists.",
            str(cm.exception),
        )

    def test_relationship_types(self):
        for rel_type in ["mother", "father", "guardian", "other"]:
            rel = ParentStudentRelationship.objects.create(
                parent=self.parent_user,
                student=self.student_user,
                relationship=rel_type,
            )
            self.assertEqual(rel.relationship, rel_type)
            rel.delete()  # Clean up for next iteration

    def test_str_representation(self):
        rel = ParentStudentRelationship.objects.create(
            parent=self.parent_user,
            student=self.student_user,
            relationship="guardian",
        )
        expected = (
            f"{self.parent_user.username} - guardian of {self.student_user.username}"
        )
        self.assertEqual(str(rel), expected)

    def test_parent_and_student_must_be_different(self):
        with self.assertRaises(ValidationError):
            ParentStudentRelationship.objects.create(
                parent=self.parent_user,
                student=self.parent_user,
                relationship="other",
            )

    def test_delete_parent_cascades(self):
        ParentStudentRelationship.objects.create(
            parent=self.parent_user,
            student=self.student_user,
            relationship="father",
        )
        self.parent_user.delete()
        self.assertEqual(ParentStudentRelationship.objects.count(), 0)

    def test_delete_student_cascades(self):
        ParentStudentRelationship.objects.create(
            parent=self.parent_user,
            student=self.student_user,
            relationship="father",
        )
        self.student_user.delete()
        self.assertEqual(ParentStudentRelationship.objects.count(), 0)


class ParentProfileModelTest(TestCase):
    def test_parent_profile_creation(self):
        from core_app.models import Organization, UserOrganizationRole, UserRole

        # Create user, org, and parent role
        user = CustomUser.objects.create_user(
            username="parentprofile", password="pass", email="<EMAIL>"
        )
        org = Organization.objects.create(name="Test Org", code="PPORG")
        parent_role, _ = UserRole.objects.get_or_create(
            code="parent", defaults={"name": "Parent"}
        )
        user_org_role = UserOrganizationRole.objects.create(
            user=user,
            organization=org,
            role=parent_role,
            is_active=True,
            is_verified=True,
        )
        # Create ParentProfile
        from .models import ParentProfile

        profile = ParentProfile.objects.create(
            user=user,
            user_org_role=user_org_role,
            id_document=None,
            phone_number="0987654321",
            is_verified=True,
        )
        self.assertEqual(profile.user, user)
        self.assertEqual(profile.user_org_role, user_org_role)
        self.assertEqual(profile.phone_number, "0987654321")
        self.assertTrue(profile.is_verified)
