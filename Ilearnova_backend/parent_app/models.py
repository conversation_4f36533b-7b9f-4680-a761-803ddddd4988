import uuid

from django.core.exceptions import ValidationError
from django.db import models

from core_app.models import CustomUser, UserOrganizationRole


class ParentProfile(models.Model):
    """
    Stores additional profile information for parents.
    Linked to CustomUser and optionally UserOrganizationRole for org-scoped data.
    """

    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="parent_profile"
    )
    user_org_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="parent_profiles",
        limit_choices_to={"role__code": "parent"},
        help_text="Parent role assignment (optional, for org-scoped data)",
    )
    id_document = models.FileField(upload_to="ids/", blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Parent Profile"
        verbose_name_plural = "Parent Profiles"

    def __str__(self):
        return self.user.username


class ParentStudentRelationship(models.Model):
    """
    Links a parent user to a student user, with relationship type.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="children_relationships",
        help_text="Parent user (must have parent role)",
    )
    student = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="parent_relationships",
        help_text="Student user (must have student role)",
    )
    relationship = models.CharField(
        max_length=32,
        choices=[
            ("mother", "Mother"),
            ("father", "Father"),
            ("guardian", "Guardian"),
            ("other", "Other"),
        ],
        default="parent",
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("parent", "student")
        verbose_name = "Parent-Student Relationship"
        verbose_name_plural = "Parent-Student Relationships"

    def __str__(self):
        return (
            f"{self.parent.username} - {self.relationship} of {self.student.username}"
        )

    def clean(self):
        if self.parent == self.student:
            raise ValidationError("Parent and student must be different users.")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
