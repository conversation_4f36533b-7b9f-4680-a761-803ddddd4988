from django.shortcuts import get_object_or_404
from rest_framework import serializers, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from core_app.models import CustomUser, ExamResult
from core_app.serializers import ExamResultSerializer

from .models import ParentStudentRelationship


class ChildSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        fields = ["id", "username", "email", "first_name", "last_name"]


class ParentChildrenListView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request):
        user = request.user
        if not hasattr(user, "parentprofile"):
            return Response(
                {"error": "Only parents can access this endpoint."},
                status=status.HTTP_403_FORBIDDEN,
            )
        parent_profile = user.parentprofile

        relationships = ParentStudentRelationship.objects.filter(parent=parent_profile)
        children = [rel.student for rel in relationships]
        serializer = ChildSerializer(children, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class ParentExamResultsView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAuthentication]

    def get(self, request, child_id, exam_id):
        user = request.user
        # Check if user is a parent
        if not hasattr(user, "parentprofile"):
            return Response(
                {"error": "Only parents can access this endpoint."},
                status=status.HTTP_403_FORBIDDEN,
            )
        # Check if child_id is one of their children
        parent_profile = user.parentprofile
        # Assuming ParentStudentRelationship
        # has parent=parent_profile and student=CustomUser

        relationship = ParentStudentRelationship.objects.filter(
            parent=parent_profile, student__id=child_id
        ).first()
        if not relationship:
            return Response(
                {"error": "This child is not associated with your account."},
                status=status.HTTP_403_FORBIDDEN,
            )
        # Get the exam result
        child = relationship.student
        exam_result = get_object_or_404(ExamResult, student=child, exam_id=exam_id)
        serializer = ExamResultSerializer(exam_result)
        return Response(serializer.data, status=status.HTTP_200_OK)
