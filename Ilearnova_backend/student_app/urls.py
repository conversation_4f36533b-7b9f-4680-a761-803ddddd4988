from django.urls import path

from .views import (
    AnnouncementListView,
    CourseMaterialListView,
    EnrollStudent,
    ExamResultsView,
    StudentAssignmentListView,
    StudentExaminationListView,
    StudentExamSubmissionListCreateView,
    StudentSubmissionListCreateView,
    SubjectListView,
    TakeExamView,
    TopicListView,
)

app_name = "student_app"

urlpatterns = [
    # Announcements
    path(
        "announcements/",
        AnnouncementListView.as_view(),
        name="announcement-list",
    ),
    # Course materials
    path(
        "classes/<uuid:class_id>/materials/",
        CourseMaterialListView.as_view(),
        name="student-class-materials",
    ),
    # Class enrollment
    path(
        "classes/<uuid:class_id>/enroll/",
        EnrollStudent.as_view(),
        name="enroll-class",
    ),
    # Subject and topic viewing
    path(
        "classes/<uuid:class_id>/subjects/",
        SubjectListView.as_view(),
        name="student-class-subjects",
    ),
    path(
        "subjects/<uuid:subject_id>/topics/",
        TopicListView.as_view(),
        name="student-subject-topics",
    ),
    # Exam taking and results
    path(
        "exams/<uuid:exam_id>/take/",
        TakeExamView.as_view(),
        name="take-exam",
    ),
    path(
        "exams/<uuid:exam_id>/results/",
        ExamResultsView.as_view(),
        name="exam-results",
    ),
    # Available assignments and examinations
    path("assignments/", StudentAssignmentListView.as_view(), name="assignment-list"),
    path(
        "examinations/", StudentExaminationListView.as_view(), name="examination-list"
    ),
    # Student submissions
    path(
        "submissions/",
        StudentSubmissionListCreateView.as_view(),
        name="submission-list-create",
    ),
    path(
        "submissions/<uuid:submission_id>/",
        StudentSubmissionListCreateView.as_view(),
        name="submission-detail",
    ),
    path(
        "exam-submissions/",
        StudentExamSubmissionListCreateView.as_view(),
        name="exam-submission-list-create",
    ),
    path(
        "exam-submissions/<uuid:exam_submission_id>/",
        StudentExamSubmissionListCreateView.as_view(),
        name="exam-submission-detail",
    ),
]
