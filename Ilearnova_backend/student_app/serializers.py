from django.utils import timezone
from rest_framework import serializers

from core_app.models import UserOrganizationRole

from .models import ExamSubmission, Submission


class SubmissionSerializer(serializers.ModelSerializer):
    """Serializer for Submission model"""

    assignment_title = serializers.Char<PERSON>ield(source="assignment.title", read_only=True)
    assignment_due_date = serializers.DateTimeField(
        source="assignment.due_date", read_only=True
    )
    student_name = serializers.CharField(
        source="student_role.user.get_full_name", read_only=True
    )
    organization_name = serializers.Char<PERSON>ield(
        source="organization.name", read_only=True
    )
    graded_by_name = serializers.Char<PERSON><PERSON>(
        source="graded_by.get_full_name", read_only=True
    )
    is_late = serializers.SerializerMethodField()

    class Meta:
        model = Submission
        fields = [
            "id",
            "assignment",
            "assignment_title",
            "assignment_due_date",
            "student_role",
            "student_name",
            "organization",
            "organization_name",
            "submitted_at",
            "content",
            "submitted_file",
            "grade",
            "graded_by",
            "graded_by_name",
            "graded_at",
            "is_late",
        ]
        read_only_fields = [
            "id",
            "organization",
            "submitted_at",
            "graded_at",
            "assignment_title",
            "assignment_due_date",
            "student_name",
            "organization_name",
            "graded_by_name",
            "is_late",
        ]

    def get_is_late(self, obj):
        """Check if submission was submitted after due date"""
        if obj.assignment.due_date and obj.submitted_at:
            return obj.submitted_at > obj.assignment.due_date
        return False

    def validate_assignment(self, value):
        """Validate that the assignment is still accepting submissions"""
        if value.due_date and timezone.now() > value.due_date:
            raise serializers.ValidationError(
                "Assignment submission deadline has passed"
            )
        return value

    def validate(self, data):
        """Validate that user has student role in the assignment's organization"""
        request = self.context.get("request")
        if request and request.user:
            assignment = data.get("assignment")
            if assignment:
                # Check if user has student role in the assignment's organization
                if not request.user.has_role_in_organization(
                    "student", assignment.organization
                ):
                    raise serializers.ValidationError(
                        "You don't have permission to submit to this assignment"
                    )
        return data

    def create(self, validated_data):
        """Create submission with proper student role assignment"""
        request = self.context.get("request")
        if request and request.user:
            assignment = validated_data["assignment"]
            # Get the user's student role in this organization
            try:
                student_role = UserOrganizationRole.objects.get(
                    user=request.user,
                    organization=assignment.organization,
                    role__code="student",
                    is_active=True,
                )
                validated_data["student_role"] = student_role
            except UserOrganizationRole.DoesNotExist:
                raise serializers.ValidationError(
                    "Student role not found for this organization"
                )
            # Organization will be auto-set by the model's save method
        return super().create(validated_data)


class ExamSubmissionSerializer(serializers.ModelSerializer):
    """Serializer for ExamSubmission model"""

    examination_title = serializers.CharField(
        source="examination.title", read_only=True
    )
    examination_start_time = serializers.DateTimeField(
        source="examination.start_time", read_only=True
    )
    examination_end_time = serializers.DateTimeField(
        source="examination.end_time", read_only=True
    )
    student_name = serializers.CharField(
        source="student_role.user.get_full_name", read_only=True
    )
    organization_name = serializers.CharField(
        source="organization.name", read_only=True
    )
    graded_by_name = serializers.CharField(
        source="graded_by.get_full_name", read_only=True
    )
    is_within_time = serializers.SerializerMethodField()

    class Meta:
        model = ExamSubmission
        fields = [
            "id",
            "examination",
            "examination_title",
            "examination_start_time",
            "examination_end_time",
            "student_role",
            "student_name",
            "organization",
            "organization_name",
            "submitted_at",
            "answers",
            "score",
            "graded_by",
            "graded_by_name",
            "graded_at",
            "is_within_time",
        ]
        read_only_fields = [
            "id",
            "organization",
            "submitted_at",
            "graded_at",
            "examination_title",
            "examination_start_time",
            "examination_end_time",
            "student_name",
            "organization_name",
            "graded_by_name",
            "is_within_time",
        ]

    def get_is_within_time(self, obj):
        """Check if submission was submitted within exam time"""
        if obj.examination.start_time and obj.examination.end_time and obj.submitted_at:
            return (
                obj.examination.start_time
                <= obj.submitted_at
                <= obj.examination.end_time
            )
        return False

    def validate_examination(self, value):
        """Validate that the examination is currently active"""
        now = timezone.now()
        if value.start_time and now < value.start_time:
            raise serializers.ValidationError("Exam has not started yet")
        if value.end_time and now > value.end_time:
            raise serializers.ValidationError("Exam time has ended")
        return value

    def validate(self, data):
        """Validate that user has student role in the exam's organization"""
        request = self.context.get("request")
        if request and request.user:
            examination = data.get("examination")
            if examination:
                # Check if user has student role in the exam's organization
                if not request.user.has_role_in_organization(
                    "student", examination.organization
                ):
                    raise serializers.ValidationError(
                        "You don't have permission to submit to this exam"
                    )
        return data

    def create(self, validated_data):
        """Create exam submission with proper student role assignment"""
        request = self.context.get("request")
        if request and request.user:
            examination = validated_data["examination"]
            # Get the user's student role in this organization
            try:
                student_role = UserOrganizationRole.objects.get(
                    user=request.user,
                    organization=examination.organization,
                    role__code="student",
                    is_active=True,
                )
                validated_data["student_role"] = student_role
            except UserOrganizationRole.DoesNotExist:
                raise serializers.ValidationError(
                    "Student role not found for this organization"
                )
            # Organization will be auto-set by the model's save method
        return super().create(validated_data)


class SubmissionCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating submissions"""

    class Meta:
        model = Submission
        fields = ["assignment", "content", "submitted_file"]

    def validate_assignment(self, value):
        """Validate that the assignment is still accepting submissions"""
        if value.due_date and timezone.now() > value.due_date:
            raise serializers.ValidationError(
                "Assignment submission deadline has passed"
            )
        return value


class ExamSubmissionCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating exam submissions"""

    class Meta:
        model = ExamSubmission
        fields = ["examination", "answers"]

    def validate_examination(self, value):
        """Validate that the examination is currently active"""
        now = timezone.now()
        if value.start_time and now < value.start_time:
            raise serializers.ValidationError("Exam has not started yet")
        if value.end_time and now > value.end_time:
            raise serializers.ValidationError("Exam time has ended")
        return value


class GradeSubmissionSerializer(serializers.ModelSerializer):
    """Serializer for grading submissions (teacher use)"""

    class Meta:
        model = Submission
        fields = ["grade"]

    def update(self, instance, validated_data):
        """Update submission with grade and grading info"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["graded_by"] = request.user
            validated_data["graded_at"] = timezone.now()
        return super().update(instance, validated_data)


class GradeExamSubmissionSerializer(serializers.ModelSerializer):
    """Serializer for grading exam submissions (teacher use)"""

    class Meta:
        model = ExamSubmission
        fields = ["score"]

    def update(self, instance, validated_data):
        """Update exam submission with score and grading info"""
        request = self.context.get("request")
        if request and request.user:
            validated_data["graded_by"] = request.user
            validated_data["graded_at"] = timezone.now()
        return super().update(instance, validated_data)
