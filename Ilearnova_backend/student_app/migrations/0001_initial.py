# Generated by Django 5.2.1 on 2025-06-27 10:21

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core_app", "0001_initial"),
        ("parent_app", "0001_initial"),
        ("teacher_app", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ExamSubmission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                ("answers", models.JSONField(default=dict)),
                ("score", models.FloatField(blank=True, null=True)),
                ("graded_at", models.DateTimeField(blank=True, null=True)),
                (
                    "examination",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_submissions",
                        to="core_app.exam",
                    ),
                ),
                (
                    "graded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="graded_exam_submissions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_submissions",
                        to="core_app.organization",
                    ),
                ),
                (
                    "student_role",
                    models.ForeignKey(
                        blank=True,
                        help_text="Student who submitted via role system",
                        limit_choices_to={"role__code": "student"},
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exam_submissions",
                        to="core_app.userorganizationrole",
                    ),
                ),
            ],
            options={
                "verbose_name": "Exam Submission",
                "verbose_name_plural": "Exam Submissions",
                "ordering": ["examination", "submitted_at"],
            },
        ),
        migrations.CreateModel(
            name="StudentProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("birthday", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent profile (optional)",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="children",
                        to="parent_app.parentprofile",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_org_role",
                    models.ForeignKey(
                        blank=True,
                        help_text="Student role assignment "
                        "(optional, for org-scoped data)",
                        limit_choices_to={"role__code": "student"},
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="student_profiles",
                        to="core_app.userorganizationrole",
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Profile",
                "verbose_name_plural": "Student Profiles",
            },
        ),
        migrations.CreateModel(
            name="Submission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                ("content", models.TextField()),
                ("submitted_file", models.FileField(upload_to="submissions/")),
                ("grade", models.FloatField(blank=True, null=True)),
                ("graded_at", models.DateTimeField(blank=True, null=True)),
                (
                    "assignment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submissions",
                        to="teacher_app.assignment",
                    ),
                ),
                (
                    "graded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="graded_submissions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submissions",
                        to="core_app.organization",
                    ),
                ),
                (
                    "student_role",
                    models.ForeignKey(
                        blank=True,
                        help_text="Student who submitted via role system",
                        limit_choices_to={"role__code": "student"},
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="submissions",
                        to="core_app.userorganizationrole",
                    ),
                ),
            ],
            options={
                "verbose_name": "Submission",
                "verbose_name_plural": "Submissions",
                "ordering": ["assignment", "submitted_at"],
            },
        ),
    ]
