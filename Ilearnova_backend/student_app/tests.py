import io
from datetime import timed<PERSON>ta

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient

from core_app.models import (
    Classes,
    CustomUser,
    Exam,
    Organization,
    Subject,
    UserOrganizationRole,
    UserRole,
)
from parent_app.models import ParentProfile
from student_app.models import ExamSubmission, Submission
from teacher_app.models import Assignment

from .models import StudentProfile


class StudentAppTestCase(TestCase):
    def setUp(self):
        # Create organization
        self.organization = Organization.objects.create(
            name="Test School",
            code="TESTSCH",
            is_active=True,
        )

        # Create roles
        self.student_role_type = UserRole.objects.create(
            name="Student", code="student", category="student"
        )
        self.teacher_role_type = UserRole.objects.create(
            name="Teacher", code="teacher", category="academic"
        )

        # Create users
        self.student_user = CustomUser.objects.create_user(
            username="student1",
            email="<EMAIL>",
            password="password123",
        )
        self.teacher_user = CustomUser.objects.create_user(
            username="teacher1",
            email="<EMAIL>",
            password="password123",
        )

        # Assign roles to users in organization
        self.student_org_role = UserOrganizationRole.objects.create(
            user=self.student_user,
            organization=self.organization,
            role=self.student_role_type,
            is_active=True,
            is_verified=True,
        )
        self.teacher_org_role = UserOrganizationRole.objects.create(
            user=self.teacher_user,
            organization=self.organization,
            role=self.teacher_role_type,
            is_active=True,
            is_verified=True,
        )

        # Create class and subject
        self.classroom = Classes.objects.create(
            name="JSS1",
            organization=self.organization,
            teacher_role=self.teacher_org_role,
        )
        self.classroom.student_roles.add(self.student_org_role)

        self.subject = Subject.objects.create(
            name="Mathematics",
            organization=self.organization,
            teacher_role=self.teacher_org_role,
            classname=self.classroom,
        )

        # Create assignment and examination
        self.assignment = Assignment.objects.create(
            title="Algebra Assignment",
            description="Solve algebra problems",
            due_date=timezone.now() + timedelta(days=1),
            subject=self.subject,
            organization=self.organization,
            created_by=self.teacher_user,
            is_active=True,
        )
        self.examination = Exam.objects.create(
            title="Midterm Exam",
            description="Math midterm",
            subject=self.subject,
            organization=self.organization,
            created_by=self.teacher_user,
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=1),
            is_active=True,
        )

        self.client = APIClient()
        self.client.login(username="student1", password="password123")

    def test_list_assignments(self):
        url = reverse("student_app:assignment-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(any(a["title"] == "Algebra Assignment" for a in response.data))

    def test_list_examinations(self):
        url = reverse("student_app:examination-list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(any(e["title"] == "Midterm Exam" for e in response.data))

    def test_create_submission(self):
        url = reverse("student_app:submission-list-create")
        testfile = io.BytesIO(b"dummy content")
        testfile.name = "dummy.txt"
        data = {
            "assignment": str(self.assignment.id),
            "content": "My solution",
            "submitted_file": testfile,
        }
        response = self.client.post(url, data, format="multipart")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(Submission.objects.count(), 1)
        submission = Submission.objects.first()
        self.assertIsNotNone(submission)
        self.assertEqual(submission.assignment, self.assignment)
        self.assertEqual(submission.student_role, self.student_org_role)
        self.assertEqual(submission.organization, self.organization)

    def test_create_submission_after_deadline(self):
        self.assignment.due_date = timezone.now() - timedelta(days=1)
        self.assignment.save()
        url = reverse("student_app:submission-list-create")
        testfile = io.BytesIO(b"dummy content")
        testfile.name = "dummy.txt"
        data = {
            "assignment": str(self.assignment.id),
            "content": "Late solution",
            "submitted_file": testfile,
        }
        response = self.client.post(url, data, format="multipart")
        self.assertEqual(response.status_code, 400)
        self.assertIn("Assignment submission deadline has passed", str(response.data))

    def test_create_exam_submission(self):
        url = reverse("student_app:exam-submission-list-create")
        data = {
            "examination": str(self.examination.id),
            "answers": {"q1": "42", "q2": "x=5"},
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 201)
        self.assertEqual(ExamSubmission.objects.count(), 1)
        exam_sub = ExamSubmission.objects.first()
        self.assertIsNotNone(exam_sub)
        self.assertEqual(exam_sub.examination, self.examination)
        self.assertEqual(exam_sub.student_role, self.student_org_role)
        self.assertEqual(exam_sub.organization, self.organization)

    def test_create_exam_submission_outside_time(self):
        self.examination.start_time = timezone.now() - timedelta(days=2)
        self.examination.end_time = timezone.now() - timedelta(days=1)
        self.examination.save()
        url = reverse("student_app:exam-submission-list-create")
        data = {
            "examination": str(self.examination.id),
            "answers": {"q1": "late"},
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, 400)
        self.assertIn("Exam time has ended", str(response.data))

    def test_permission_denied_for_non_student(self):
        # Remove student role
        self.student_org_role.is_active = False
        self.student_org_role.save()
        url = reverse("student_app:submission-list-create")
        testfile = io.BytesIO(b"dummy content")
        testfile.name = "dummy.txt"
        data = {
            "assignment": str(self.assignment.id),
            "content": "Should fail",
            "submitted_file": testfile,
        }
        response = self.client.post(url, data, format="multipart")
        self.assertEqual(response.status_code, 403)

    def test_list_submissions(self):
        # Create a submission
        Submission.objects.create(
            assignment=self.assignment,
            student_role=self.student_org_role,
            organization=self.organization,
            content="Test content",
            submitted_file="submissions/test.txt",
        )
        url = reverse("student_app:submission-list-create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(any(s["content"] == "Test content" for s in response.data))

    def test_list_exam_submissions(self):
        # Create an exam submission
        ExamSubmission.objects.create(
            examination=self.examination,
            student_role=self.student_org_role,
            organization=self.organization,
            answers={"q1": "A"},
        )
        url = reverse("student_app:exam-submission-list-create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(any(e["answers"]["q1"] == "A" for e in response.data))


class StudentProfileModelTest(TestCase):
    def test_student_profile_creation(self):

        # Create user, org, and student role
        user = CustomUser.objects.create_user(
            username="studentprofile", password="pass", email="<EMAIL>"
        )
        org = Organization.objects.create(name="Test Org", code="SPORG")
        student_role, _ = UserRole.objects.get_or_create(
            code="student", defaults={"name": "Student"}
        )
        user_org_role = UserOrganizationRole.objects.create(
            user=user,
            organization=org,
            role=student_role,
            is_active=True,
            is_verified=True,
        )
        # Create parent user and profile
        parent_user = CustomUser.objects.create_user(
            username="parentforstudent", password="pass", email="<EMAIL>"
        )
        parent_role, _ = UserRole.objects.get_or_create(
            code="parent", defaults={"name": "Parent"}
        )
        parent_user_org_role = UserOrganizationRole.objects.create(
            user=parent_user,
            organization=org,
            role=parent_role,
            is_active=True,
            is_verified=True,
        )
        parent_profile = ParentProfile.objects.create(
            user=parent_user,
            user_org_role=parent_user_org_role,
            phone_number="1112223333",
            is_verified=True,
        )
        # Create StudentProfile
        profile = StudentProfile.objects.create(
            user=user,
            user_org_role=user_org_role,
            address="456 School Rd",
            birthday="2010-05-15",
            parent=parent_profile,
        )
        self.assertEqual(profile.user, user)
        self.assertEqual(profile.user_org_role, user_org_role)
        self.assertEqual(profile.address, "456 School Rd")
        self.assertEqual(str(profile.birthday), "2010-05-15")
        self.assertEqual(profile.parent, parent_profile)
