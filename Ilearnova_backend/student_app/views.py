from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import generics, serializers, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication

from core_app.models import (
    Announcement,
    Classes,
    Exam,
    ExamResult,
    Question,
    StudentResponse,
    Subject,
)
from core_app.serializers import (
    AnnouncementSerializer,
    CourseMaterialSerializer,
    ExamResultSerializer,
    ExamSerializer,
    SubjectSerializer,
    TopicSerializer,
)
from core_app.views import StudentPermission
from teacher_app.models import Assignment
from teacher_app.serializers import AssignmentSerializer

from .models import ExamSubmission, Submission
from .serializers import (
    ExamSubmissionCreateSerializer,
    ExamSubmissionSerializer,
    SubmissionCreateSerializer,
    SubmissionSerializer,
)


class EnrollStudent(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    authentication_classes = [JWTAuthentication]

    def post(self, request, class_id):
        user = request.user

        if not hasattr(user, "student"):
            return Response(
                {"message": "Only students can register for a class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        student = user.student
        if not class_id:
            return Response(
                {"error": "class_id is required."}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            class_instance = Classes.objects.get(id=class_id)
        except Classes.DoesNotExist:
            return Response(
                {"error": "Class not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if class_instance.students.filter(id=student.id).exists():
            return Response(
                {
                    "message": f"You are already registered to "
                    f"the class '{class_instance.name}'."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add student to the class's students
        class_instance.students.add(student)
        class_instance.save()

        return Response(
            {
                "message": f"You have successfully registered to "
                f"the class '{class_instance.name}'."
            },
            status=status.HTTP_200_OK,
        )


class SubjectListView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    serializer_class = SubjectSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request, class_id):
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can view subjects."},
                status=status.HTTP_403_FORBIDDEN,
            )

        class_instance = get_object_or_404(Classes, id=class_id)
        student = request.user.student
        if not class_instance.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subjects = class_instance.subjects.all()
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TopicListView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    serializer_class = TopicSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request, subject_id):
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can view topics."},
                status=status.HTTP_403_FORBIDDEN,
            )

        subject = get_object_or_404(Subject, id=subject_id)
        student = request.user.student
        class_instance = subject.classname
        if not class_instance.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in the class for this subject."},
                status=status.HTTP_403_FORBIDDEN,
            )

        topics = subject.topics.all()
        serializer = TopicSerializer(topics, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AnnouncementListView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    serializer_class = AnnouncementSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request):
        # Announcements for all organizations the student belongs to
        user = request.user
        orgs = user.get_organizations()
        announcements = Announcement.objects.filter(organization__in=orgs).order_by(
            "-created_at"
        )
        serializer = AnnouncementSerializer(announcements, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CourseMaterialListView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    serializer_class = CourseMaterialSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request, class_id):
        user = request.user
        class_obj = get_object_or_404(Classes, id=class_id)
        # Check if student is enrolled in the class
        if (
            not hasattr(user, "student")
            or not class_obj.students.filter(id=user.student.id).exists()
        ):
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )
        materials = class_obj.materials.all().order_by("-created_at")
        serializer = CourseMaterialSerializer(materials, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TakeExamView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    authentication_classes = [JWTAuthentication]

    def get(self, request, exam_id):
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can take exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        exam = get_object_or_404(Exam, id=exam_id)
        student = request.user.student

        # Check if student is enrolled in the class
        if not exam.subject.classname.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Check if exam is active
        now = timezone.now()
        if not (exam.start_time <= now <= exam.end_time):
            return Response(
                {"error": "Exam is not currently active."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if student has already submitted
        if ExamResult.objects.filter(student=student, exam=exam).exists():
            return Response(
                {"error": "You have already submitted this exam."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        questions = exam.questions.all()
        from core_app.serializers import QuestionSerializer

        serializer = QuestionSerializer(questions, many=True)
        return Response(
            {"exam": ExamSerializer(exam).data, "questions": serializer.data},
            status=status.HTTP_200_OK,
        )

    def post(self, request, exam_id):
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can submit exams."},
                status=status.HTTP_403_FORBIDDEN,
            )

        exam = get_object_or_404(Exam, id=exam_id)
        student = request.user.student

        # Check if student is enrolled
        if not exam.subject.classname.students.filter(id=student.id).exists():
            return Response(
                {"error": "You are not enrolled in this class."},
                status=status.HTTP_403_FORBIDDEN,
            )

        # Check if exam is still active
        now = timezone.now()
        if now > exam.end_time:
            return Response(
                {"error": "Exam submission time has ended."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not (exam.start_time <= now <= exam.end_time):
            return Response(
                {"error": "Exam is not currently active."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check for existing submission
        if ExamResult.objects.filter(student=student, exam=exam).exists():
            return Response(
                {"error": "You have already submitted this exam."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Process responses
        responses = request.data.get("responses", [])
        correct_count = 0
        total_marks = 0

        for response in responses:
            question_id = response.get("question")
            selected_option = response.get("selected_option")

            if not question_id or not selected_option:
                continue

            question = get_object_or_404(Question, id=question_id, exam=exam)
            StudentResponse.objects.create(
                student=student,
                exam=exam,
                question=question,
                selected_option=selected_option,
            )

            if selected_option == question.correct_option:
                correct_count += 1
                total_marks += question.mark

        # Calculate result
        percentage = (
            (total_marks / exam.num_of_questions) * 100
            if exam.num_of_questions > 0
            else 0
        )

        # Save final result
        ExamResult.objects.create(
            student=student,
            exam=exam,
            score=total_marks,
            total_marks=exam.total_marks,
            percentage=percentage,
        )

        return Response(
            {
                "message": "Exam submitted successfully",
                "score": total_marks,
                "total_marks": exam.total_marks,
                "total_questions": exam.num_of_questions,
            },
            status=status.HTTP_201_CREATED,
        )


class ExamResultsView(APIView):
    permission_classes = [IsAuthenticated, StudentPermission]
    serializer_class = ExamResultSerializer
    authentication_classes = [JWTAuthentication]

    def get(self, request, exam_id):
        exam = get_object_or_404(Exam, id=exam_id)
        if not hasattr(request.user, "student"):
            return Response(
                {"error": "Only students can view their exam results."},
                status=status.HTTP_403_FORBIDDEN,
            )
        student = request.user.student
        result = get_object_or_404(ExamResult, student=student, exam=exam)
        serializer = ExamResultSerializer(result)
        return Response(serializer.data, status=status.HTTP_200_OK)


class StudentAssignmentListView(generics.ListAPIView):
    """List all assignments available to the student"""

    serializer_class = AssignmentSerializer
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_queryset(self):
        """Filter assignments by student's organizations"""
        user = self.request.user
        # Check if this is a schema generation request
        if getattr(self, "swagger_fake_view", False):
            return Assignment.objects.none()

        # Check if user is authenticated
        if not user.is_authenticated:
            return Assignment.objects.none()

        # Get all organizations where user has student role
        student_orgs = user.get_role_organizations("student")
        return Assignment.objects.filter(
            organization__in=student_orgs, is_active=True
        ).order_by("-created_at")


class StudentExaminationListView(generics.ListAPIView):
    """List all exams available to the student"""

    serializer_class = ExamSerializer
    permission_classes = [IsAuthenticated, StudentPermission]

    def get_queryset(self):
        """Filter exams by student's organizations"""
        user = self.request.user
        # Check if this is a schema generation request
        if getattr(self, "swagger_fake_view", False):
            return Exam.objects.none()

        # Check if user is authenticated
        if not user.is_authenticated:
            return Exam.objects.none()

        student_orgs = user.get_role_organizations("student")
        return Exam.objects.filter(
            organization__in=student_orgs, is_active=True
        ).order_by("-start_time")


class StudentSubmissionListCreateView(generics.ListCreateAPIView):
    """List student's submissions and create new ones"""

    permission_classes = [IsAuthenticated, StudentPermission]

    def get_serializer_class(self):
        if self.request.method == "POST":
            return SubmissionCreateSerializer
        return SubmissionSerializer

    def get_queryset(self):
        """Filter submissions by current student"""
        user = self.request.user
        student_orgs = user.get_role_organizations("student")
        return Submission.objects.filter(
            organization__in=student_orgs, student_role__user=user
        ).order_by("-submitted_at")

    def perform_create(self, serializer):
        """Create submission with proper student role assignment"""
        assignment = serializer.validated_data["assignment"]
        # Get the user's student role in this organization
        from core_app.models import UserOrganizationRole

        try:
            student_role = UserOrganizationRole.objects.get(
                user=self.request.user,
                organization=assignment.organization,
                role__code="student",
                is_active=True,
            )
            serializer.save(student_role=student_role)
        except UserOrganizationRole.DoesNotExist:
            raise serializers.ValidationError(
                "Student role not found for this organization"
            )


class StudentExamSubmissionListCreateView(generics.ListCreateAPIView):
    """List student's exam submissions and create new ones"""

    permission_classes = [IsAuthenticated, StudentPermission]

    def get_serializer_class(self):
        if self.request.method == "POST":
            return ExamSubmissionCreateSerializer
        return ExamSubmissionSerializer

    def get_queryset(self):
        """Filter exam submissions by current student"""
        user = self.request.user
        student_orgs = user.get_role_organizations("student")
        return ExamSubmission.objects.filter(
            organization__in=student_orgs, student_role__user=user
        ).order_by("-submitted_at")

    def perform_create(self, serializer):
        """Create exam submission with proper student role assignment"""
        exam = serializer.validated_data.get("exam") or serializer.validated_data.get(
            "examination"
        )
        # Get the user's student role in this organization
        from core_app.models import UserOrganizationRole

        try:
            student_role = UserOrganizationRole.objects.get(
                user=self.request.user,
                organization=exam.organization,
                role__code="student",
                is_active=True,
            )
            serializer.save(student_role=student_role)
        except UserOrganizationRole.DoesNotExist:
            raise serializers.ValidationError(
                "Student role not found for this organization"
            )
