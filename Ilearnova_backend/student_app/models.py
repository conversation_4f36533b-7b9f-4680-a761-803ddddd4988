import uuid

from django.db import models

from core_app.models import CustomUser, Exam, Organization, UserOrganizationRole
from parent_app.models import ParentProfile
from teacher_app.models import Assignment


class StudentProfile(models.Model):
    """
    Stores additional profile information for students.
    Linked to CustomUser and optionally UserOrganizationRole for org-scoped data.
    """

    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="student_profile"
    )
    user_org_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="student_profiles",
        limit_choices_to={"role__code": "student"},
        help_text="Student role assignment (optional, for org-scoped data)",
    )
    address = models.CharField(max_length=255, blank=True, null=True)
    birthday = models.DateField(null=True, blank=True)
    parent = models.ForeignKey(
        ParentProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="children",
        help_text="Parent profile (optional)",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Student Profile"
        verbose_name_plural = "Student Profiles"

    def __str__(self):
        return self.user.username


class Submission(models.Model):
    """
    Represents a student's submission for an assignment.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    assignment = models.ForeignKey(
        Assignment, on_delete=models.CASCADE, related_name="submissions"
    )
    # Role-based student assignment
    student_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name="submissions",
        null=True,
        blank=True,
        limit_choices_to={"role__code": "student"},
        help_text="Student who submitted via role system",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="submissions",
        null=True,
        blank=True,
    )
    submitted_at = models.DateTimeField(auto_now_add=True)
    content = models.TextField()
    submitted_file = models.FileField(upload_to="submissions/")
    grade = models.FloatField(null=True, blank=True)
    graded_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="graded_submissions",
    )
    graded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Submission"
        verbose_name_plural = "Submissions"
        ordering = ["assignment", "submitted_at"]

    def __str__(self):
        student_name = (
            self.student_role.user.username if self.student_role else "Unknown"
        )
        return f"{self.assignment.title} - {student_name}"

    def save(self, *args, **kwargs):
        # Auto-set organization from assignment
        if self.assignment and not self.organization:
            self.organization = self.assignment.organization
        super().save(*args, **kwargs)


class ExamSubmission(models.Model):
    """
    Represents a student's submission for an examination.
    Updated to support organization-scoped data.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    examination = models.ForeignKey(
        Exam, on_delete=models.CASCADE, related_name="exam_submissions"
    )
    # Role-based student assignment
    student_role = models.ForeignKey(
        UserOrganizationRole,
        on_delete=models.CASCADE,
        related_name="exam_submissions",
        null=True,
        blank=True,
        limit_choices_to={"role__code": "student"},
        help_text="Student who submitted via role system",
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="exam_submissions",
        null=True,
        blank=True,
    )
    submitted_at = models.DateTimeField(auto_now_add=True)
    answers = models.JSONField(default=dict)
    score = models.FloatField(null=True, blank=True)
    graded_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="graded_exam_submissions",
    )
    graded_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Exam Submission"
        verbose_name_plural = "Exam Submissions"
        ordering = ["examination", "submitted_at"]

    def __str__(self):
        student_name = (
            self.student_role.user.username if self.student_role else "Unknown"
        )
        return f"{self.examination.title} - {student_name}"

    def save(self, *args, **kwargs):
        # Auto-set organization from examination
        if self.examination and not self.organization:
            self.organization = self.examination.organization
        super().save(*args, **kwargs)
