from django.contrib import admin

from .models import ExamSubmission, Submission


@admin.register(Submission)
class SubmissionAdmin(admin.ModelAdmin):
    list_display = [
        "assignment",
        "student_role",
        "organization",
        "submitted_at",
        "grade",
    ]
    list_filter = ["organization", "submitted_at", "graded_at"]
    search_fields = [
        "assignment__title",
        "student_role__user__username",
        "organization__name",
    ]
    readonly_fields = ["submitted_at", "graded_at"]


@admin.register(ExamSubmission)
class ExamSubmissionAdmin(admin.ModelAdmin):
    list_display = [
        "examination",
        "student_role",
        "organization",
        "submitted_at",
        "score",
    ]
    list_filter = ["organization", "submitted_at", "graded_at"]
    search_fields = [
        "examination__title",
        "student_role__user__username",
        "organization__name",
    ]
    readonly_fields = ["submitted_at", "graded_at"]
