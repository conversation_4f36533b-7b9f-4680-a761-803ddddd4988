aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
bcrypt==4.3.0
black==24.4.2
blis==1.3.0
catalogue==2.0.10
certifi==2025.6.15
cffi==1.17.1
cfgv==3.4.0
channels==4.2.2
charset-normalizer==3.4.2
click==8.2.1
cloudpathlib==0.21.1
confection==0.1.5
cryptography==45.0.4
cymem==2.0.11
distlib==0.3.9
Django==5.2.1
django-allauth==65.8.1
django-cors-headers==4.7.0
django-channels==0.7.0
djangorestframework==3.16.0
djangorestframework_simplejwt==5.5.0
dnspython==2.7.0
drf-yasg==1.21.10
ecdsa==0.19.1
email_validator==2.2.0
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl#sha256=1932429db727d4bff3deed6b34cfc05df17794f4a52eeb26cf8928f7c1a0fb85
fastapi==0.115.14
filelock==3.18.0
flake8==7.0.0
greenlet==3.2.3
gunicorn==23.0.0
h11==0.16.0
identify==2.6.12
idna==3.10
inflection==0.5.1
isort==5.13.2
Jinja2==3.1.6
langcodes==3.5.0
language_data==1.3.0
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mccabe==0.7.0
mdurl==0.1.2
murmurhash==1.0.13
mypy_extensions==1.1.0
nodeenv==1.9.1
numpy==2.3.1
oauthlib==3.3.1
packaging==25.0
passlib==1.7.4
pathspec==0.12.1
pillow==11.2.1
platformdirs==4.3.8
pre-commit==3.7.1
preshed==3.0.10
psycopg2-binary==2.9.10
pyasn1==0.6.1
pycodestyle==2.11.1
pycparser==2.22
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pyflakes==3.2.0
Pygments==2.19.2
PyJWT==2.9.0
python-dotenv==1.1.0
python-jose==3.5.0
pytz==2025.2
PyYAML==6.0.2
requests==2.32.4
requests-oauthlib==2.0.0
rich==14.0.0
rsa==4.9.1
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.41
sqlparse==0.5.3
srsly==2.5.1
starlette==0.46.2
thinc==8.3.6
tqdm==4.67.1
typer==0.16.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.5.0
uvicorn==0.34.3
virtualenv==20.31.2
wasabi==1.1.3
weasel==0.4.1
wrapt==1.17.2
