from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

# Custom security definition that automatically adds Bearer prefix
security_definitions = {
    "JWT": {
        "type": "apiKey",
        "name": "Authorization",
        "in": "header",
        "description": 'Enter your JWT token only (without "Bearer" prefix)',
    }
}

schema_view = get_schema_view(
    openapi.Info(
        title="Ilearnova API",
        default_version="v1",
        description="API documentation for Ilearnova backend",
    ),
    public=True,
    permission_classes=[AllowAny],
)

urlpatterns = [
    path("admin/", admin.site.urls),
    # JWT Authentication endpoints
    path("api/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("api/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    # New role-based API structure
    path("api/", include("core_app.urls")),
    path("api/teacher/", include("teacher_app.urls")),
    path("api/student/", include("student_app.urls")),
    path("api/parent/", include("parent_app.urls")),
    path("api/admin/", include("admin_app.urls")),
    # API Documentation
    path(
        "api/swagger/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path(
        "api/redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"
    ),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
