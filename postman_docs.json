{"info": {"name": "Ilearnova API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "https://api.ilearnova.com/api"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"teacher1\",\n  \"password\": \"password123\",\n  \"organization_code\": \"ORG001\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login/", "host": ["{{baseUrl}}"], "path": ["auth", "login", ""]}}, "response": []}, {"name": "Register Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newadmin\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"newpassword123\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/", "host": ["{{baseUrl}}"], "path": ["auth", "register", ""]}}, "response": []}, {"name": "Register Teacher", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newteacher\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"newpassword123\",\n  \"first_name\": \"New\",\n  \"last_name\": \"Teacher\",\n  \"organization_code\": \"ORG001\",\n  \"role_code\": \"teacher\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/teacher/", "host": ["{{baseUrl}}"], "path": ["auth", "register", "teacher", ""]}}, "response": []}, {"name": "Register Student", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newstudent\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"newpassword123\",\n  \"first_name\": \"New\",\n  \"last_name\": \"Student\",\n  \"organization_code\": \"ORG001\",\n  \"role_code\": \"student\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/student/", "host": ["{{baseUrl}}"], "path": ["auth", "register", "student", ""]}}, "response": []}, {"name": "Register Parent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newparent\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"newpassword123\",\n  \"first_name\": \"New\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"organization_code\": \"ORG001\",\n  \"role_code\": \"parent\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register/parent/", "host": ["{{baseUrl}}"], "path": ["auth", "register", "parent", ""]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/verify-email/?token=<verification_token>", "host": ["{{baseUrl}}"], "path": ["verify-email", ""], "query": [{"key": "token", "value": "<verification_token>"}]}}, "response": []}]}, {"name": "User", "item": [{"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/auth/profile/", "host": ["{{baseUrl}}"], "path": ["auth", "profile", ""]}}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"Updated First Name\",\n  \"last_name\": \"Updated Last Name\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/profile/", "host": ["{{baseUrl}}"], "path": ["auth", "profile", ""]}}, "response": []}]}, {"name": "Teacher", "item": [{"name": "Create Class", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Class Name\"\n}"}, "url": {"raw": "{{baseUrl}}/teacher/classes/", "host": ["{{baseUrl}}"], "path": ["teacher", "classes", ""]}}, "response": []}, {"name": "Get Class Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/teacher/classes/{{class_id}}/", "host": ["{{baseUrl}}"], "path": ["teacher", "classes", "{{class_id}}", ""]}}, "response": []}, {"name": "Create Subject", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Subject Name\"\n}"}, "url": {"raw": "{{baseUrl}}/teacher/classes/{{class_id}}/subjects/", "host": ["{{baseUrl}}"], "path": ["teacher", "classes", "{{class_id}}", "subjects", ""]}}, "response": []}, {"name": "Create Topic", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Topic Name\"\n}"}, "url": {"raw": "{{baseUrl}}/teacher/subjects/{{subject_id}}/topics/", "host": ["{{baseUrl}}"], "path": ["teacher", "subjects", "{{subject_id}}", "topics", ""]}}, "response": []}, {"name": "Create Announcement", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Announcement\",\n  \"message\": \"This is a new announcement.\"\n}"}, "url": {"raw": "{{baseUrl}}/teacher/announcements/create/", "host": ["{{baseUrl}}"], "path": ["teacher", "announcements", "create", ""]}}, "response": []}, {"name": "Create Course Material", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "New Course Material", "type": "text"}, {"key": "description", "value": "Description of the course material.", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/teacher/classes/{{class_id}}/materials/", "host": ["{{baseUrl}}"], "path": ["teacher", "classes", "{{class_id}}", "materials", ""]}}, "response": []}, {"name": "Create Assignment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Assignment\",\n  \"description\": \"This is a new assignment.\",\n  \"due_date\": \"2025-12-31T23:59:59Z\",\n  \"subject\": \"{{subject_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/teacher/assignments/", "host": ["{{baseUrl}}"], "path": ["teacher", "assignments", ""]}}, "response": []}, {"name": "Get Assignment Submissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/teacher/assignments/{{assignment_id}}/submissions/", "host": ["{{baseUrl}}"], "path": ["teacher", "assignments", "{{assignment_id}}", "submissions", ""]}}, "response": []}]}, {"name": "Student", "item": [{"name": "Enroll in Class", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/classes/{{class_id}}/enroll/", "host": ["{{baseUrl}}"], "path": ["student", "classes", "{{class_id}}", "enroll", ""]}}, "response": []}, {"name": "Get Subjects in Class", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/classes/{{class_id}}/subjects/", "host": ["{{baseUrl}}"], "path": ["student", "classes", "{{class_id}}", "subjects", ""]}}, "response": []}, {"name": "Get Topics in Subject", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/subjects/{{subject_id}}/topics/", "host": ["{{baseUrl}}"], "path": ["student", "subjects", "{{subject_id}}", "topics", ""]}}, "response": []}, {"name": "Get Announcements", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/announcements/", "host": ["{{baseUrl}}"], "path": ["student", "announcements", ""]}}, "response": []}, {"name": "Get Course Materials", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/classes/{{class_id}}/materials/", "host": ["{{baseUrl}}"], "path": ["student", "classes", "{{class_id}}", "materials", ""]}}, "response": []}, {"name": "Take <PERSON>am", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"responses\": [\n    {\n      \"question\": \"{{question_id}}\",\n      \"selected_option\": \"A\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/student/exams/{{exam_id}}/take/", "host": ["{{baseUrl}}"], "path": ["student", "exams", "{{exam_id}}", "take", ""]}}, "response": []}, {"name": "Get Exam Results", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/student/exams/{{exam_id}}/results/", "host": ["{{baseUrl}}"], "path": ["student", "exams", "{{exam_id}}", "results", ""]}}, "response": []}, {"name": "Submit Assignment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "body": {"mode": "formdata", "formdata": [{"key": "assignment", "value": "{{assignment_id}}", "type": "text"}, {"key": "content", "value": "This is my submission.", "type": "text"}, {"key": "submitted_file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/student/submissions/", "host": ["{{baseUrl}}"], "path": ["student", "submissions", ""]}}, "response": []}]}, {"name": "Parent", "item": [{"name": "Get Children", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/parent/children/", "host": ["{{baseUrl}}"], "path": ["parent", "children", ""]}}, "response": []}, {"name": "Get Child Exam Results", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/parent/children/{{child_id}}/exams/{{exam_id}}/results/", "host": ["{{baseUrl}}"], "path": ["parent", "children", "{{child_id}}", "exams", "{{exam_id}}", "results", ""]}}, "response": []}]}, {"name": "Admin", "item": [{"name": "Get Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/admin/users/", "host": ["{{baseUrl}}"], "path": ["admin", "users", ""]}}, "response": []}, {"name": "Get Classes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/admin/classes/", "host": ["{{baseUrl}}"], "path": ["admin", "classes", ""]}}, "response": []}, {"name": "Create Class", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Class Name\",\n  \"teacher_role\": \"{{teacher_role_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/classes/create/", "host": ["{{baseUrl}}"], "path": ["admin", "classes", "create", ""]}}, "response": []}, {"name": "Get Subjects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <jwt_token>"}], "url": {"raw": "{{baseUrl}}/admin/subjects/", "host": ["{{baseUrl}}"], "path": ["admin", "subjects", ""]}}, "response": []}]}]}