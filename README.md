# Ilearnova Backend

A robust, multi-tenant educational platform backend built with Django and Django REST Framework. Designed for schools and organizations to manage users, roles, classes, subjects, assignments, exams, and more, with a flexible role-based access system and organization-level data isolation.

---

## Table of Contents

- [Ilearnova Backend](#ilearnova-backend)
  - [Table of Contents](#table-of-contents)
  - [Project Overview](#project-overview)
  - [Architecture](#architecture)
  - [Tech Stack](#tech-stack)
  - [Directory Structure](#directory-structure)
  - [Django Apps](#django-apps)
    - [1. **core\_app**](#1-core_app)
    - [2. **admin\_app**](#2-admin_app)
    - [3. **teacher\_app**](#3-teacher_app)
    - [4. **student\_app**](#4-student_app)
    - [5. **parent\_app**](#5-parent_app)
  - [Setup \& Installation](#setup--installation)
    - [1. **Clone the Repository**](#1-clone-the-repository)
    - [2. **Create \& Activate Virtual Environment**](#2-create--activate-virtual-environment)
    - [3. **Install Dependencies**](#3-install-dependencies)
    - [4. **Configure Environment Variables**](#4-configure-environment-variables)
    - [5. **Apply Migrations**](#5-apply-migrations)
    - [6. **Create Superuser (Admin)**](#6-create-superuser-admin)
    - [7. **Run the Development Server**](#7-run-the-development-server)
  - [Database](#database)
  - [Static \& Media Files](#static--media-files)
  - [API Structure \& Authentication](#api-structure--authentication)
  - [API Documentation](#api-documentation)
  - [Code Style \& Quality](#code-style--quality)
  - [Testing](#testing)
  - [Contribution Guidelines](#contribution-guidelines)
  - [License](#license)
  - [Contact](#contact)

---

## Project Overview

Ilearnova Backend is the core server-side component for the Ilearnova platform, supporting multi-organization (school) management, dynamic user roles, and a full suite of educational features. It is designed for scalability, security, and extensibility, making it easy for new developers to onboard and contribute.

---

## Architecture

- **Multi-Tenancy:** Each organization (school/institution) has isolated data.
- **Role-Based Access:** Flexible roles (admin, teacher, student, parent, etc.) with dynamic assignment and permissions.
- **Organization-Scoped Data:** Classes, subjects, assignments, exams, and submissions are all linked to organizations.
- **RESTful API:** All features are exposed via a secure, documented REST API.
- **JWT Authentication:** Secure, stateless authentication with support for role-based access control.
- **Swagger/OpenAPI:** Interactive API documentation for easy exploration and testing.

---

## Tech Stack

- **Language:** Python 3.11
- **Framework:** Django 5.2.1
- **API:** Django REST Framework, drf-yasg (Swagger)
- **Auth:** JWT (djangorestframework-simplejwt), django-allauth
- **Database:** PostgreSQL
- **Media:** Pillow
- **Dev Tools:** flake8, black, isort, pre-commit

---

## Directory Structure

```
Ilearnova-be/
├── .flake8
├── .gitignore
├── .pre-commit-config.yaml
├── LICENSE
├── pyproject.toml
├── README.md
├── Ilearnova_backend/
│   ├── manage.py
│   ├── requirements.txt
│   ├── Ilearnova_backend/
│   │   ├── settings.py
│   │   ├── urls.py
│   │   ├── wsgi.py
│   │   └── asgi.py
│   ├── admin_app/
│   ├── core_app/
│   ├── parent_app/
│   ├── student_app/
│   ├── teacher_app/
│   ├── media/
│   ├── static/
│   ├── staticfiles/
│   └── templates/
└── Ilearnova/
```

---

## Django Apps

### 1. **core_app**
- **Purpose:** Central models and logic for organizations, users, roles, classes, subjects, and topics.
- **Key Models:**
  - `Organization`: Represents a school/institution.
  - `CustomUser`: Extended user model with organization and role support.
  - `UserRole`, `UserOrganizationRole`: Flexible, dynamic role system.
  - `Classes`, `Subject`, `Topic`: Academic structure, all organization-scoped.

### 2. **admin_app**
- **Purpose:** Admin-specific API endpoints for managing users, classes, and subjects within an organization.
- **Features:** List users/classes/subjects, create classes (admin-only access).

### 3. **teacher_app**
- **Purpose:** Teacher-specific features and endpoints.
- **Key Models:**
  - `Assignment`: Assignments for subjects.
  - `Examination`: Exams for subjects.
- **Features:** Create/manage assignments and exams.

### 4. **student_app**
- **Purpose:** Student-specific features and endpoints.
- **Key Models:**
  - `Submission`: Assignment submissions.
  - `ExamSubmission`: Exam submissions.
- **Features:** Submit assignments/exams, view grades.

### 5. **parent_app**
- **Purpose:** Parent/guardian features.
- **Key Model:**
  - `ParentStudentRelationship`: Links parents to students.
- **Features:** Monitor student progress, relationship management.

---

## Setup & Installation

### 1. **Clone the Repository**
```bash
git clone <repo-url>
cd ilearnova-be/Ilearnova_backend
```

### 2. **Create & Activate Virtual Environment**
```bash
python3.11 -m venv venv
source venv/bin/activate
```

### 3. **Install Dependencies**
```bash
pip install -r requirements.txt
```

### 4. **Configure Environment Variables**
- Copy `.env.example` to `.env` (create one if not present).
- Set the following variables:

```
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
DB_NAME=ilearnova_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=localhost
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=<EMAIL>
ADMIN_NOTIFICATION_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
ACCESS_TOKEN_LIFETIME_HOURS=24
REFRESH_TOKEN_LIFETIME_DAYS=10
```

### 5. **Apply Migrations**
```bash
python manage.py migrate
```

### 6. **Create Superuser (Admin)**
```bash
python manage.py createsuperuser
```

### 7. **Run the Development Server**
```bash
python manage.py runserver
```

---

## Database

- **Default:** PostgreSQL (configure via `.env`)
- **Alternative:** SQLite (see commented config in `settings.py` for development/testing)

---

## Static & Media Files

- **Static files:** Place in `Ilearnova_backend/static/`
- **Media files (uploads):** Stored in `Ilearnova_backend/media/`
- **Collected static files:** `Ilearnova_backend/staticfiles/`
- **URLs:** `/static/` and `/media/` (served automatically in DEBUG mode)

---

## API Structure & Authentication

- **Admin Interface:** `/admin/`
- **JWT Auth:**
  - Obtain token: `POST /api/token/`
  - Refresh token: `POST /api/token/refresh/`
- **Role-Based API Endpoints:**
  - Core: `/api/`
  - Teacher: `/api/teacher/`
  - Student: `/api/student/`
  - Admin: `/api/admin/`
  - (Parent endpoints: `/api/parent/` - to be implemented)
- **Authentication:** All endpoints require JWT Bearer token unless otherwise specified.
- **Custom User Model:** `core_app.CustomUser` (email-unique, organization/role aware)

---

## API Documentation

- **Swagger UI:** [http://localhost:8000/api/swagger/](http://localhost:8000/api/swagger/)
- **ReDoc:** [http://localhost:8000/api/redoc/](http://localhost:8000/api/redoc/)
- **Usage:** Authorize with JWT token via the "Authorize" button in Swagger UI.

---

## Code Style & Quality

- **Formatter:** [Black](https://black.readthedocs.io/en/stable/) (`pyproject.toml` config)
- **Import Sorting:** [isort](https://pycqa.github.io/isort/)
- **Linting:** [flake8](https://flake8.pycqa.org/)
- **Pre-commit Hooks:** [pre-commit](https://pre-commit.com/)
- **Run checks:**
  ```bash
  black .
  isort .
  flake8 .
  pre-commit run --all-files
  ```

---

## Testing

- **Run all tests:**
  ```bash
  python manage.py test
  ```
- **Test locations:** Each app contains its own `tests.py`.

---

## Contribution Guidelines

- Fork the repository and create a feature branch.
- Follow code style and quality checks (Black, isort, flake8, pre-commit).
- Write tests for new features or bug fixes.
- Submit a pull request with a clear description.

---

## License

This project is licensed under the terms of the [LICENSE](LICENSE) file.

---

## Contact

For questions or support, contact the project admin at `<EMAIL>`.
